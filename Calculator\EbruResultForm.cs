using System;
using System.Drawing;
using System.Windows.Forms;

namespace Calculator
{
    public partial class EbruResultForm : Form
    {
        private TextBox txtResult;
        private Button btnClose;

        public EbruResultForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.txtResult = new TextBox();
            this.btnClose = new Button();
            this.SuspendLayout();
            
            // 
            // txtResult
            // 
            this.txtResult.Location = new Point(12, 12);
            this.txtResult.Multiline = true;
            this.txtResult.Name = "txtResult";
            this.txtResult.ReadOnly = true;
            this.txtResult.ScrollBars = ScrollBars.Both;
            this.txtResult.Size = new Size(760, 500);
            this.txtResult.TabIndex = 0;
            this.txtResult.Font = new Font("Consolas", 9F, FontStyle.Regular, GraphicsUnit.Point);
            this.txtResult.WordWrap = false;
            
            // 
            // btnClose
            // 
            this.btnClose.Location = new Point(697, 518);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new Size(75, 23);
            this.btnClose.TabIndex = 1;
            this.btnClose.Text = "Kapat";
            this.btnClose.UseVisualStyleBackColor = true;
            this.btnClose.Click += new EventHandler(this.btnClose_Click);
            
            // 
            // EbruResultForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(784, 553);
            this.Controls.Add(this.btnClose);
            this.Controls.Add(this.txtResult);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "EbruResultForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "Ebru Metodları Sonuçları";
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        public void SetResultText(string text)
        {
            if (txtResult != null)
            {
                txtResult.Text = text;
                txtResult.SelectionStart = 0;
                txtResult.SelectionLength = 0;
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
