
using System.Text;
using System.Collections.Generic;

namespace Calculator;

public partial class Form1 : Form
{
    private double currentValue = 0;
    private string operation = "";
    private bool isNewEntry = true;

    public Form1()
    {
        InitializeComponent();
    }

    private void btn0_Click(object sender, EventArgs e)
    {
        AppendNumber("0");
    }

    private void btn1_Click(object sender, EventArgs e)
    {
        AppendNumber("1");
    }

    private void btn2_Click(object sender, EventArgs e)
    {
        AppendNumber("2");
    }

    private void btn3_Click(object sender, EventArgs e)
    {
        AppendNumber("3");
    }

    private void btn4_Click(object sender, EventArgs e)
    {
        AppendNumber("4");
    }

    private void btn5_Click(object sender, EventArgs e)
    {
        AppendNumber("5");
    }

    private void btn6_Click(object sender, EventArgs e)
    {
        AppendNumber("6");
    }

    private void btn7_Click(object sender, EventArgs e)
    {
        AppendNumber("7");
    }

    private void btn8_Click(object sender, EventArgs e)
    {
        AppendNumber("8");
    }

    private void btn9_Click(object sender, EventArgs e)
    {
        AppendNumber("9");
    }

    private void btnPlus_Click(object sender, EventArgs e)
    {
        SetOperation("+");
    }

    private void btnMinus_Click(object sender, EventArgs e)
    {
        SetOperation("-");
    }

    private void btnMultiply_Click(object sender, EventArgs e)
    {
        SetOperation("*");
    }

    private void btnDivide_Click(object sender, EventArgs e)
    {
        SetOperation("/");
    }

    private void btnEquals_Click(object sender, EventArgs e)
    {
        Calculate();
    }

    private void btnClear_Click(object sender, EventArgs e)
    {
        Clear();
    }

    private void AppendNumber(string number)
    {
        if (isNewEntry)
        {
            txtDisplay.Text = number;
            isNewEntry = false;
        }
        else
        {
            txtDisplay.Text += number;
        }
    }

    private void SetOperation(string op)
    {
        if (!string.IsNullOrEmpty(operation))
        {
            Calculate();
        }
        currentValue = double.Parse(txtDisplay.Text);
        operation = op;
        isNewEntry = true;
    }

    private void Calculate()
    {
        if (!string.IsNullOrEmpty(operation))
        {
            double secondValue = double.Parse(txtDisplay.Text);
            switch (operation)
            {
                case "+":
                    currentValue += secondValue;
                    break;
                case "-":
                    currentValue -= secondValue;
                    break;
                case "*":
                    currentValue *= secondValue;
                    break;
                case "/":
                    if (secondValue != 0)
                        currentValue /= secondValue;
                    else
                        MessageBox.Show("Cannot divide by zero");
                    break;
            }
            txtDisplay.Text = currentValue.ToString();
            operation = "";
            isNewEntry = true;
        }
    }

    private void Clear()
    {
        txtDisplay.Text = "0";
        currentValue = 0;
        operation = "";
        isNewEntry = true;
    }

    private void btnDeneme1_Click(object sender, EventArgs e)
    {
        string[] words = String3();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < words.Length; i++)
        {
            sb.Append(words[i]);
            if (i < words.Length - 1)
                sb.Append(" ");
        }
        string message = sb.ToString();
        int tokenCount = message.Length / 4;
        int charCount = message.Length;
        MessageBox.Show($"Number of tokens: {tokenCount}\nNumber of words: {words.Length}\nNumber of characters: {charCount}\n\n{message}");
    }

    private void btnDeneme2_Click(object sender, EventArgs e)
    {
        string[] words = String3();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < words.Length; i++)
        {
            sb.Append(words[i]);
            if (i < words.Length - 1)
                sb.Append(" ");
        }
        string message = sb.ToString();
        txtOutput.Text = message;
    }

    private void btnDeneme3_Click(object sender, EventArgs e)
    {
        string[] words = String4();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < words.Length; i++)
        {
            sb.Append(words[i]);
            if (i < words.Length - 1)
                sb.Append(" ");
        }
        string message = sb.ToString();
        MessageBox.Show($"Number of words: {words.Length}\n\n{message}");
    }

    private void btnDeneme4_Click(object sender, EventArgs e)
    {
        MessageBox.Show("merhaba");
    }

    private void btnDeneme5_Click(object sender, EventArgs e)
    {
        string message = "merhaba " + new string('A', 1900);
        MessageBox.Show(message);
    }

    private void btnDeneme6_Click(object sender, EventArgs e)
    {
        string message = "merhaba " + new string('A', 19900);
        MessageBox.Show(message);
    }

    private void btnDeneme7_Click(object sender, EventArgs e)
    {
        Random random = new Random();
        string[] letters = { "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z" };
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 500; i++)
        {
            string word = "";
            int wordLength = random.Next(3, 9); // 3 to 8 letters
            for (int j = 0; j < wordLength; j++)
            {
                word += letters[random.Next(letters.Length)];
            }
            sb.Append(word + " ");
        }
        string message = sb.ToString().Trim();
        MessageBox.Show(message);
    }

    private string[] String1()
    {
        string[] provinces = new string[]
        {
            "Adana", "Adıyaman", "Afyonkarahisar", "Ağrı", "Aksaray", "Amasya", "Ankara", "Antalya", "Ardahan", "Artvin",
            "Aydın", "Balıkesir", "Bartın", "Batman", "Bayburt", "Bilecik", "Bingöl", "Bitlis", "Bolu", "Burdur",
            "Bursa", "Çanakkale", "Çankırı", "Çorum", "Denizli", "Diyarbakır", "Düzce", "Edirne", "Elazığ", "Erzincan",
            "Erzurum", "Eskişehir", "Gaziantep", "Giresun", "Gümüşhane", "Hakkari", "Hatay", "Iğdır", "Isparta", "İstanbul",
            "İzmir", "Kahramanmaraş", "Karabük", "Karaman", "Kars", "Kastamonu", "Kayseri", "Kırıkkale", "Kırklareli", "Kırşehir",
            "Kilis", "Kocaeli", "Konya", "Kütahya", "Malatya", "Manisa", "Mardin", "Mersin", "Muğla", "Muş",
            "Nevşehir", "Niğde", "Ordu", "Osmaniye", "Rize", "Sakarya", "Samsun", "Siirt", "Sinop", "Sivas",
            "Şanlıurfa", "Şırnak", "Tekirdağ", "Tokat", "Trabzon", "Tunceli", "Uşak", "Van", "Yalova", "Yozgat",
            "Zonguldak"
        };
        return provinces;
    }

    private string[] String2()
    {
        string[] words = new string[]
        {
            "apple", "banana", "cherry", "date", "elderberry", "fig", "grape", "honeydew", "kiwi", "lemon",
            "mango", "nectarine", "orange", "papaya", "quince", "raspberry", "strawberry", "tangerine", "ugli", "vanilla",
            "watermelon", "xigua", "yellow", "zucchini", "avocado", "blueberry", "coconut", "dragonfruit", "eggplant", "falafel",
            "guava", "huckleberry", "iceberg", "jackfruit", "kumquat", "lime", "mulberry", "nutmeg", "olive", "paprika",
            "quinoa", "radish", "spinach", "tomato", "uglyfruit", "violet", "walnut", "xanthium", "yam", "zest",
            "almond", "broccoli", "carrot", "durian", "endive", "fennel", "ginger", "horseradish", "indigo", "juniper",
            "kale", "lettuce", "mint", "nut", "onion", "parsley", "quail", "rhubarb", "sage", "thyme",
            "uva", "verbena", "wilt", "xylem", "yarrow", "zinnia", "basil", "cilantro", "dill", "escarole",
            "fava", "garlic", "hyssop", "iris", "jicama", "ketchup", "lavender", "marjoram", "nasturtium", "oregano",
            "pepper", "quassia", "rosemary"
        };
        return words;
    }

    private string[] String3()
    {
        string[] numbers = new string[] { "one", "two", "three", "four", "five", "six", "seven", "eight", "nine", "ten" };
        return numbers;
    }

    private string[] String4()
    {
        string[] colors = new string[] { "red", "green", "blue", "yellow", "purple", "orange", "pink", "brown", "black", "white", "gray", "silver", "gold", "bronze", "copper", "indigo", "violet", "turquoise", "teal", "lime", "magenta", "cyan", "maroon", "navy", "olive", "aqua", "fuchsia", "chartreuse", "crimson", "khaki", "lavender", "plum", "salmon", "scarlet", "sienna", "tan", "thistle", "wheat", "cat", "dog", "bird", "fish", "tree", "house", "car", "book", "pen", "chair", "table", "door", "window", "light", "dark", "big", "small", "happy", "sad", "fast", "slow", "hot", "cold", "new", "old", "young", "old", "man", "woman", "child", "food", "water", "air", "fire", "earth", "sun", "moon", "star", "planet", "river", "mountain", "ocean", "forest", "city", "village", "school", "hospital", "shop", "work", "play", "run", "walk", "jump", "sing", "dance", "read", "write", "think", "feel", "love", "hate", "good", "bad", "true", "false", "one", "two", "three", "four", "five", "six", "seven", "eight", "nine", "ten", "eleven", "twelve", "thirteen", "fourteen", "fifteen", "sixteen", "seventeen", "eighteen", "nineteen", "twenty", "thirty", "forty", "fifty", "sixty", "seventy", "eighty", "ninety", "hundred", "thousand", "million", "apple", "banana", "cherry", "date", "elderberry", "fig", "grape", "honeydew", "kiwi", "lemon", "mango", "nectarine", "orange", "papaya", "quince", "raspberry", "strawberry", "tangerine", "ugli", "vanilla", "watermelon", "xigua", "zucchini", "avocado", "blueberry", "coconut", "dragonfruit", "eggplant", "falafel", "guava", "huckleberry", "iceberg", "jackfruit", "kumquat", "lime", "mulberry", "nutmeg", "olive", "paprika", "quinoa", "radish", "spinach", "tomato", "uglyfruit", "violet", "walnut", "xanthium", "yam", "zest", "almond", "broccoli", "carrot", "durian", "endive", "fennel", "ginger", "horseradish", "indigo", "juniper", "kale", "lettuce", "mint", "nut", "onion", "parsley", "quail", "rhubarb", "sage", "thyme", "uva", "verbena", "wilt", "xylem", "yarrow", "zinnia", "basil", "cilantro", "dill", "escarole", "fava", "garlic", "hyssop", "iris", "jicama", "ketchup", "lavender", "marjoram", "nasturtium", "oregano", "pepper", "quassia", "rosemary", "apple", "banana", "carrot", "date", "egg", "fig", "grape", "honey", "ice", "jam", "kiwi", "lemon", "mango", "nut", "orange", "pear", "quince", "rice", "strawberry", "tomato", "ugli", "vanilla", "watermelon", "xigua", "yam", "zucchini", "ant", "bear", "cat", "dog", "elephant", "fox", "goat", "horse", "iguana", "jaguar", "kangaroo", "lion", "monkey", "newt", "ostrich", "panda", "quail", "rabbit", "snake", "tiger", "urchin", "vulture", "wolf", "xerus", "yak", "zebra", "book", "chair", "door", "egg", "fan", "glass", "hat", "ink", "jar", "key", "lamp", "map", "net", "oil", "pen", "quill", "ring", "shoe", "table", "umbrella", "vase", "watch", "xylophone", "yarn", "zipper", "action", "beauty", "cloud", "dream", "energy", "flower", "garden", "heart", "island", "journey", "knowledge", "lightning", "mountain", "nature", "ocean", "peace", "quiet", "rainbow", "sunset", "time", "universe", "victory", "wonder", "youth", "zenith", "abandon", "brave", "create", "dawn", "echo", "freedom", "glory", "hope", "inspire", "joy", "kindness", "love", "magic", "night", "open", "power", "quest", "rise", "spirit", "truth", "unity", "vision", "wisdom", "xray", "yesterday", "zest", "adventure", "bliss", "charm", "delight", "embrace", "fame", "grace", "honor", "imagination", "justice", "kiss", "liberty", "melody", "noble", "origin", "passion", "quest", "radiance", "serene", "triumph", "uplift", "valor", "whisper", "xenial", "yearn", "zephyr", "amber", "beige", "cerulean", "denim", "emerald", "fawn", "gamboge", "hazel", "ivory", "jade", "khaki", "lilac", "mauve", "navy", "ochre", "periwinkle", "quartz", "russet", "sepia", "taupe", "umber", "vermilion", "wisteria", "xanthic", "yellow", "zaffre", "apricot", "boysenberry", "cantaloupe", "durian", "elderberry", "fig", "guava", "huckleberry", "imbe", "jackfruit", "kumquat", "lychee", "mandarin", "nectarine", "papaya", "quince", "raspberry", "soursop", "tangerine", "ugli", "vanilla", "watermelon", "xigua", "yuzu", "ziziphus", "alligator", "badger", "camel", "dolphin", "eagle", "flamingo", "gorilla", "hippo", "ibex", "jaguar", "kangaroo", "lemur", "moose", "narwhal", "otter", "penguin", "quokka", "rhino", "seal", "toucan", "urchin", "vulture", "walrus", "xerus", "yak", "zebra", "buffalo", "coyote", "donkey", "emu", "ferret", "gazelle", "hyena", "impala", "jerboa", "koala", "llama", "mule", "numbat", "opossum", "porcupine", "quoll", "rat", "sloth", "tapir", "uakari", "vicuna", "wombat", "xerus", "yak", "zorilla" };
        return colors;
    }

    private void hesapla()
    {
        if (!string.IsNullOrEmpty(operation))
        {
            double secondValue = double.Parse(txtDisplay.Text);
            switch (operation)
            {
                case "+":
                    currentValue += secondValue;
                    break;
                case "-":
                    currentValue -= secondValue;
                    break;
                case "*":
                    currentValue *= secondValue;
                    break;
                case "/":
                    if (secondValue != 0)
                        currentValue /= secondValue;
                    else
                        MessageBox.Show("Cannot divide by zero");
                    break;
            }
            txtDisplay.Text = currentValue.ToString();
            operation = "";
            isNewEntry = true;
        }
    }
    private void ekle1()
    {
        Random random = new Random();
        double sum = 0;
        for (int i = 0; i < 100; i++)
        {
            int num = random.Next(1, 101); // Random numbers from 1 to 100
            sum += num;
        }
        MessageBox.Show($"100 rastgele sayının toplamı: {sum}");
    }
    private string[] String5()
    {
        string[] words = new string[]
        {
            "word1","word2","word3","word4","word5","word6","word7","word8","word9","word10",
            "word11","word12","word13","word14","word15","word16","word17","word18","word19","word20",
            "word21","word22","word23","word24","word25","word26","word27","word28","word29","word30",
            "word31","word32","word33","word34","word35","word36","word37","word38","word39","word40",
            "word41","word42","word43","word44","word45","word46","word47","word48","word49","word50",
            "word51","word52","word53","word54","word55","word56","word57","word58","word59","word60",
            "word61","word62","word63","word64","word65","word66","word67","word68","word69","word70",
            "word71","word72","word73","word74","word75","word76","word77","word78","word79","word80",
            "word81","word82","word83","word84","word85","word86","word87","word88","word89","word90",
            "word91","word92","word93","word94","word95","word96","word97","word98","word99","word100",
            "word101","word102","word103","word104","word105","word106","word107","word108","word109","word110",
            "word111","word112","word113","word114","word115","word116","word117","word118","word119","word120",
            "word121","word122","word123","word124","word125","word126","word127","word128","word129","word130",
            "word131","word132","word133","word134","word135","word136","word137","word138","word139","word140",
            "word141","word142","word143","word144","word145","word146","word147","word148","word149","word150",
            "word151","word152","word153","word154","word155","word156","word157","word158","word159","word160",
            "word161","word162","word163","word164","word165","word166","word167","word168","word169","word170",
            "word171","word172","word173","word174","word175","word176","word177","word178","word179","word180",
            "word181","word182","word183","word184","word185","word186","word187","word188","word189","word190",
            "word191","word192","word193","word194","word195","word196","word197","word198","word199","word200"
        };
        return words;
    }
    
    private string[] baha1()
    {
        string[] words = new string[]
        {
            "baha_word1", "baha_word2", "baha_word3", "baha_word4", "baha_word5", "baha_word6", "baha_word7", "baha_word8", "baha_word9", "baha_word10",
            "baha_word11", "baha_word12", "baha_word13", "baha_word14", "baha_word15", "baha_word16", "baha_word17", "baha_word18", "baha_word19", "baha_word20",
            "baha_word21", "baha_word22", "baha_word23", "baha_word24", "baha_word25", "baha_word26", "baha_word27", "baha_word28", "baha_word29", "baha_word30",
            "baha_word31", "baha_word32", "baha_word33", "baha_word34", "baha_word35", "baha_word36", "baha_word37", "baha_word38", "baha_word39", "baha_word40",
            "baha_word41", "baha_word42", "baha_word43", "baha_word44", "baha_word45", "baha_word46", "baha_word47", "baha_word48", "baha_word49", "baha_word50",
            "baha_word51", "baha_word52", "baha_word53", "baha_word54", "baha_word55", "baha_word56", "baha_word57", "baha_word58", "baha_word59", "baha_word60",
            "baha_word61", "baha_word62", "baha_word63", "baha_word64", "baha_word65", "baha_word66", "baha_word67", "baha_word68", "baha_word69", "baha_word70",
            "baha_word71", "baha_word72", "baha_word73", "baha_word74", "baha_word75", "baha_word76", "baha_word77", "baha_word78", "baha_word79", "baha_word80",
            "baha_word81", "baha_word82", "baha_word83", "baha_word84", "baha_word85", "baha_word86", "baha_word87", "baha_word88", "baha_word89", "baha_word90",
            "baha_word91", "baha_word92", "baha_word93", "baha_word94", "baha_word95", "baha_word96", "baha_word97", "baha_word98", "baha_word99", "baha_word100",
            "baha_word101", "baha_word102", "baha_word103", "baha_word104", "baha_word105", "baha_word106", "baha_word107", "baha_word108", "baha_word109", "baha_word110",
            "baha_word111", "baha_word112", "baha_word113", "baha_word114", "baha_word115", "baha_word116", "baha_word117", "baha_word118", "baha_word119", "baha_word120",
            "baha_word121", "baha_word122", "baha_word123", "baha_word124", "baha_word125", "baha_word126", "baha_word127", "baha_word128", "baha_word129", "baha_word130",
            "baha_word131", "baha_word132", "baha_word133", "baha_word134", "baha_word135", "baha_word136", "baha_word137", "baha_word138", "baha_word139", "baha_word140",
            "baha_word141", "baha_word142", "baha_word143", "baha_word144", "baha_word145", "baha_word146", "baha_word147", "baha_word148", "baha_word149", "baha_word150",
            "baha_word151", "baha_word152", "baha_word153", "baha_word154", "baha_word155", "baha_word156", "baha_word157", "baha_word158", "baha_word159", "baha_word160",
            "baha_word161", "baha_word162", "baha_word163", "baha_word164", "baha_word165", "baha_word166", "baha_word167", "baha_word168", "baha_word169", "baha_word170",
            "baha_word171", "baha_word172", "baha_word173", "baha_word174", "baha_word175", "baha_word176", "baha_word177", "baha_word178", "baha_word179", "baha_word180",
            "baha_word181", "baha_word182", "baha_word183", "baha_word184", "baha_word185", "baha_word186", "baha_word187", "baha_word188", "baha_word189", "baha_word190",
            "baha_word191", "baha_word192", "baha_word193", "baha_word194", "baha_word195", "baha_word196", "baha_word197", "baha_word198", "baha_word199", "baha_word200",
            "baha_word201", "baha_word202", "baha_word203", "baha_word204", "baha_word205", "baha_word206", "baha_word207", "baha_word208", "baha_word209", "baha_word210",
            "baha_word211", "baha_word212", "baha_word213", "baha_word214", "baha_word215", "baha_word216", "baha_word217", "baha_word218", "baha_word219", "baha_word220",
            "baha_word221", "baha_word222", "baha_word223", "baha_word224", "baha_word225", "baha_word226", "baha_word227", "baha_word228", "baha_word229", "baha_word230",
            "baha_word231", "baha_word232", "baha_word233", "baha_word234", "baha_word235", "baha_word236", "baha_word237", "baha_word238", "baha_word239", "baha_word240",
            "baha_word241", "baha_word242", "baha_word243", "baha_word244", "baha_word245", "baha_word246", "baha_word247", "baha_word248", "baha_word249", "baha_word250",
            "baha_word251", "baha_word252", "baha_word253", "baha_word254", "baha_word255", "baha_word256", "baha_word257", "baha_word258", "baha_word259", "baha_word260",
            "baha_word261", "baha_word262", "baha_word263", "baha_word264", "baha_word265", "baha_word266", "baha_word267", "baha_word268", "baha_word269", "baha_word270",
            "baha_word271", "baha_word272", "baha_word273", "baha_word274", "baha_word275", "baha_word276", "baha_word277", "baha_word278", "baha_word279", "baha_word280",
            "baha_word281", "baha_word282", "baha_word283", "baha_word284", "baha_word285", "baha_word286", "baha_word287", "baha_word288", "baha_word289", "baha_word290",
            "baha_word291", "baha_word292", "baha_word293", "baha_word294", "baha_word295", "baha_word296", "baha_word297", "baha_word298", "baha_word299", "baha_word300",
            "baha_word301", "baha_word302", "baha_word303", "baha_word304", "baha_word305", "baha_word306", "baha_word307", "baha_word308", "baha_word309", "baha_word310",
            "baha_word311", "baha_word312", "baha_word313", "baha_word314", "baha_word315", "baha_word316", "baha_word317", "baha_word318", "baha_word319", "baha_word320",
            "baha_word321", "baha_word322", "baha_word323", "baha_word324", "baha_word325", "baha_word326", "baha_word327", "baha_word328", "baha_word329", "baha_word330",
            "baha_word331", "baha_word332", "baha_word333", "baha_word334", "baha_word335", "baha_word336", "baha_word337", "baha_word338", "baha_word339", "baha_word340",
            "baha_word341", "baha_word342", "baha_word343", "baha_word344", "baha_word345", "baha_word346", "baha_word347", "baha_word348", "baha_word349", "baha_word350",
            "baha_word351", "baha_word352", "baha_word353", "baha_word354", "baha_word355", "baha_word356", "baha_word357", "baha_word358", "baha_word359", "baha_word360",
            "baha_word361", "baha_word362", "baha_word363", "baha_word364", "baha_word365", "baha_word366", "baha_word367", "baha_word368", "baha_word369", "baha_word370",
            "baha_word371", "baha_word372", "baha_word373", "baha_word374", "baha_word375", "baha_word376", "baha_word377", "baha_word378", "baha_word379", "baha_word380",
            "baha_word381", "baha_word382", "baha_word383", "baha_word384", "baha_word385", "baha_word386", "baha_word387", "baha_word388", "baha_word389", "baha_word390",
            "baha_word391", "baha_word392", "baha_word393", "baha_word394", "baha_word395", "baha_word396", "baha_word397", "baha_word398", "baha_word399", "baha_word400"
        };
        return words;
    }
    
    private string[] baha2()
    {
        string[] words = new string[]
        {
            "baha2_word1", "baha2_word2", "baha2_word3", "baha2_word4", "baha2_word5", "baha2_word6", "baha2_word7", "baha2_word8", "baha2_word9", "baha2_word10",
            "baha2_word11", "baha2_word12", "baha2_word13", "baha2_word14", "baha2_word15", "baha2_word16", "baha2_word17", "baha2_word18", "baha2_word19", "baha2_word20",
            "baha2_word21", "baha2_word22", "baha2_word23", "baha2_word24", "baha2_word25", "baha2_word26", "baha2_word27", "baha2_word28", "baha2_word29", "baha2_word30",
            "baha2_word31", "baha2_word32", "baha2_word33", "baha2_word34", "baha2_word35", "baha2_word36", "baha2_word37", "baha2_word38", "baha2_word39", "baha2_word40",
            "baha2_word41", "baha2_word42", "baha2_word43", "baha2_word44", "baha2_word45", "baha2_word46", "baha2_word47", "baha2_word48", "baha2_word49", "baha2_word50",
            "baha2_word51", "baha2_word52", "baha2_word53", "baha2_word54", "baha2_word55", "baha2_word56", "baha2_word57", "baha2_word58", "baha2_word59", "baha2_word60",
            "baha2_word61", "baha2_word62", "baha2_word63", "baha2_word64", "baha2_word65", "baha2_word66", "baha2_word67", "baha2_word68", "baha2_word69", "baha2_word70",
            "baha2_word71", "baha2_word72", "baha2_word73", "baha2_word74", "baha2_word75", "baha2_word76", "baha2_word77", "baha2_word78", "baha2_word79", "baha2_word80",
            "baha2_word81", "baha2_word82", "baha2_word83", "baha2_word84", "baha2_word85", "baha2_word86", "baha2_word87", "baha2_word88", "baha2_word89", "baha2_word90",
            "baha2_word91", "baha2_word92", "baha2_word93", "baha2_word94", "baha2_word95", "baha2_word96", "baha2_word97", "baha2_word98", "baha2_word99", "baha2_word100",
            "baha2_word101", "baha2_word102", "baha2_word103", "baha2_word104", "baha2_word105", "baha2_word106", "baha2_word107", "baha2_word108", "baha2_word109", "baha2_word110",
            "baha2_word111", "baha2_word112", "baha2_word113", "baha2_word114", "baha2_word115", "baha2_word116", "baha2_word117", "baha2_word118", "baha2_word119", "baha2_word120",
            "baha2_word121", "baha2_word122", "baha2_word123", "baha2_word124", "baha2_word125", "baha2_word126", "baha2_word127", "baha2_word128", "baha2_word129", "baha2_word130",
            "baha2_word131", "baha2_word132", "baha2_word133", "baha2_word134", "baha2_word135", "baha2_word136", "baha2_word137", "baha2_word138", "baha2_word139", "baha2_word140",
            "baha2_word141", "baha2_word142", "baha2_word143", "baha2_word144", "baha2_word145", "baha2_word146", "baha2_word147", "baha2_word148", "baha2_word149", "baha2_word150",
            "baha2_word151", "baha2_word152", "baha2_word153", "baha2_word154", "baha2_word155", "baha2_word156", "baha2_word157", "baha2_word158", "baha2_word159", "baha2_word160",
            "baha2_word161", "baha2_word162", "baha2_word163", "baha2_word164", "baha2_word165", "baha2_word166", "baha2_word167", "baha2_word168", "baha2_word169", "baha2_word170",
            "baha2_word171", "baha2_word172", "baha2_word173", "baha2_word174", "baha2_word175", "baha2_word176", "baha2_word177", "baha2_word178", "baha2_word179", "baha2_word180",
            "baha2_word181", "baha2_word182", "baha2_word183", "baha2_word184", "baha2_word185", "baha2_word186", "baha2_word187", "baha2_word188", "baha2_word189", "baha2_word190",
            "baha2_word191", "baha2_word192", "baha2_word193", "baha2_word194", "baha2_word195", "baha2_word196", "baha2_word197", "baha2_word198", "baha2_word199", "baha2_word200",
            "baha2_word201", "baha2_word202", "baha2_word203", "baha2_word204", "baha2_word205", "baha2_word206", "baha2_word207", "baha2_word208", "baha2_word209", "baha2_word210",
            "baha2_word211", "baha2_word212", "baha2_word213", "baha2_word214", "baha2_word215", "baha2_word216", "baha2_word217", "baha2_word218", "baha2_word219", "baha2_word220",
            "baha2_word221", "baha2_word222", "baha2_word223", "baha2_word224", "baha2_word225", "baha2_word226", "baha2_word227", "baha2_word228", "baha2_word229", "baha2_word230",
            "baha2_word231", "baha2_word232", "baha2_word233", "baha2_word234", "baha2_word235", "baha2_word236", "baha2_word237", "baha2_word238", "baha2_word239", "baha2_word240",
            "baha2_word241", "baha2_word242", "baha2_word243", "baha2_word244", "baha2_word245", "baha2_word246", "baha2_word247", "baha2_word248", "baha2_word249", "baha2_word250",
            "baha2_word251", "baha2_word252", "baha2_word253", "baha2_word254", "baha2_word255", "baha2_word256", "baha2_word257", "baha2_word258", "baha2_word259", "baha2_word260",
            "baha2_word261", "baha2_word262", "baha2_word263", "baha2_word264", "baha2_word265", "baha2_word266", "baha2_word267", "baha2_word268", "baha2_word269", "baha2_word270",
            "baha2_word271", "baha2_word272", "baha2_word273", "baha2_word274", "baha2_word275", "baha2_word276", "baha2_word277", "baha2_word278", "baha2_word279", "baha2_word280",
            "baha2_word281", "baha2_word282", "baha2_word283", "baha2_word284", "baha2_word285", "baha2_word286", "baha2_word287", "baha2_word288", "baha2_word289", "baha2_word290",
            "baha2_word291", "baha2_word292", "baha2_word293", "baha2_word294", "baha2_word295", "baha2_word296", "baha2_word297", "baha2_word298", "baha2_word299", "baha2_word300",
            "baha2_word301", "baha2_word302", "baha2_word303", "baha2_word304", "baha2_word305", "baha2_word306", "baha2_word307", "baha2_word308", "baha2_word309", "baha2_word310",
            "baha2_word311", "baha2_word312", "baha2_word313", "baha2_word314", "baha2_word315", "baha2_word316", "baha2_word317", "baha2_word318", "baha2_word319", "baha2_word320",
            "baha2_word321", "baha2_word322", "baha2_word323", "baha2_word324", "baha2_word325", "baha2_word326", "baha2_word327", "baha2_word328", "baha2_word329", "baha2_word330",
            "baha2_word331", "baha2_word332", "baha2_word333", "baha2_word334", "baha2_word335", "baha2_word336", "baha2_word337", "baha2_word338", "baha2_word339", "baha2_word340",
            "baha2_word341", "baha2_word342", "baha2_word343", "baha2_word344", "baha2_word345", "baha2_word346", "baha2_word347", "baha2_word348", "baha2_word349", "baha2_word350",
            "baha2_word351", "baha2_word352", "baha2_word353", "baha2_word354", "baha2_word355", "baha2_word356", "baha2_word357", "baha2_word358", "baha2_word359", "baha2_word360",
            "baha2_word361", "baha2_word362", "baha2_word363", "baha2_word364", "baha2_word365", "baha2_word366", "baha2_word367", "baha2_word368", "baha2_word369", "baha2_word370",
            "baha2_word371", "baha2_word372", "baha2_word373", "baha2_word374", "baha2_word375", "baha2_word376", "baha2_word377", "baha2_word378", "baha2_word379", "baha2_word380",
            "baha2_word381", "baha2_word382", "baha2_word383", "baha2_word384", "baha2_word385", "baha2_word386", "baha2_word387", "baha2_word388", "baha2_word389", "baha2_word390",
            "baha2_word391", "baha2_word392", "baha2_word393", "baha2_word394", "baha2_word395", "baha2_word396", "baha2_word397", "baha2_word398", "baha2_word399", "baha2_word400"
        };
        return words;
    }
    
    private string[] String6()
    {
        string[] words = new string[]
        {
            "word1","word2","word3","word4","word5","word6","word7","word8","word9","word10",
            "word11","word12","word13","word14","word15","word16","word17","word18","word19","word20",
            "word21","word22","word23","word24","word25","word26","word27","word28","word29","word30",
            "word31","word32","word33","word34","word35","word36","word37","word38","word39","word40",
            "word41","word42","word43","word44","word45","word46","word47","word48","word49","word50",
            "word51","word52","word53","word54","word55","word56","word57","word58","word59","word60",
            "word61","word62","word63","word64","word65","word66","word67","word68","word69","word70",
            "word71","word72","word73","word74","word75","word76","word77","word78","word79","word80",
            "word81","word82","word83","word84","word85","word86","word87","word88","word89","word90",
            "word91","word92","word93","word94","word95","word96","word97","word98","word99","word100",
            "word101","word102","word103","word104","word105","word106","word107","word108","word109","word110",
            "word111","word112","word113","word114","word115","word116","word117","word118","word119","word120",
            "word121","word122","word123","word124","word125","word126","word127","word128","word129","word130",
            "word131","word132","word133","word134","word135","word136","word137","word138","word139","word140",
            "word141","word142","word143","word144","word145","word146","word147","word148","word149","word150",
            "word151","word152","word153","word154","word155","word156","word157","word158","word159","word160",
            "word161","word162","word163","word164","word165","word166","word167","word168","word169","word170",
            "word171","word172","word173","word174","word175","word176","word177","word178","word179","word180",
            "word181","word182","word183","word184","word185","word186","word187","word188","word189","word190",
            "word191","word192","word193","word194","word195","word196","word197","word198","word199","word200",
            "word201","word202","word203","word204","word205","word206","word207","word208","word209","word210",
            "word211","word212","word213","word214","word215","word216","word217","word218","word219","word220",
            "word221","word222","word223","word224","word225","word226","word227","word228","word229","word230",
            "word231","word232","word233","word234","word235","word236","word237","word238","word239","word240",
            "word241","word242","word243","word244","word245","word246","word247","word248","word249","word250",
            "word251","word252","word253","word254","word255","word256","word257","word258","word259","word260",
            "word261","word262","word263","word264","word265","word266","word267","word268","word269","word270",
            "word271","word272","word273","word274","word275","word276","word277","word278","word279","word280",
            "word281","word282","word283","word284","word285","word286","word287","word288","word289","word290",
            "word291","word292","word293","word294","word295","word296","word297","word298","word299","word300",
            "word301","word302","word303","word304","word305","word306","word307","word308","word309","word310",
            "word311","word312","word313","word314","word315","word316","word317","word318","word319","word320",
            "word321","word322","word323","word324","word325","word326","word327","word328","word329","word330",
            "word331","word332","word333","word334","word335","word336","word337","word338","word339","word340",
            "word341","word342","word343","word344","word345","word346","word347","word348","word349","word350",
            "word351","word352","word353","word354","word355","word356","word357","word358","word359","word360",
            "word361","word362","word363","word364","word365","word366","word367","word368","word369","word370",
            "word371","word372","word373","word374","word375","word376","word377","word378","word379","word380",
            "word381","word382","word383","word384","word385","word386","word387","word388","word389","word390",
            "word391","word392","word393","word394","word395","word396","word397","word398","word399","word400"
        };
        return words;
    }
    private void btnDeneme8_Click(object sender, EventArgs e)
    {
        string[] words = String6();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < words.Length; i++)
        {
            sb.Append(words[i]);
            if (i < words.Length - 1)
                sb.Append(" ");
        }
        string message = sb.ToString();
        int tokenCount = message.Length / 4;
        int charCount = message.Length;
        int wordCount = words.Length;
        MessageBox.Show($"Number of tokens: {tokenCount}\nNumber of words: {wordCount}\nNumber of characters: {charCount}\n\n{message}");
    }

    private void btnDeneme9_Click(object sender, EventArgs e)
    {
        string[] words = String6();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < words.Length; i++)
        {
            sb.Append(words[i]);
            if (i < words.Length - 1)
                sb.Append(" ");
        }
        string message = sb.ToString();
        int tokenCount = message.Length / 4;
        int charCount = message.Length;
        int wordCount = words.Length;
        MessageBox.Show($"Number of tokens: {tokenCount}\nNumber of words: {wordCount}\nNumber of characters: {charCount}\n\n{message}");
    }

    private string[] yasar3()
    {
        string[] words = new string[]
        {
            "y3_word1","y3_word2","y3_word3","y3_word4","y3_word5","y3_word6","y3_word7","y3_word8","y3_word9","y3_word10",
            "y3_word11","y3_word12","y3_word13","y3_word14","y3_word15","y3_word16","y3_word17","y3_word18","y3_word19","y3_word20",
            "y3_word21","y3_word22","y3_word23","y3_word24","y3_word25","y3_word26","y3_word27","y3_word28","y3_word29","y3_word30",
            "y3_word31","y3_word32","y3_word33","y3_word34","y3_word35","y3_word36","y3_word37","y3_word38","y3_word39","y3_word40",
            "y3_word41","y3_word42","y3_word43","y3_word44","y3_word45","y3_word46","y3_word47","y3_word48","y3_word49","y3_word50",
            "y3_word51","y3_word52","y3_word53","y3_word54","y3_word55","y3_word56","y3_word57","y3_word58","y3_word59","y3_word60",
            "y3_word61","y3_word62","y3_word63","y3_word64","y3_word65","y3_word66","y3_word67","y3_word68","y3_word69","y3_word70",
            "y3_word71","y3_word72","y3_word73","y3_word74","y3_word75","y3_word76","y3_word77","y3_word78","y3_word79","y3_word80",
            "y3_word81","y3_word82","y3_word83","y3_word84","y3_word85","y3_word86","y3_word87","y3_word88","y3_word89","y3_word90",
            "y3_word91","y3_word92","y3_word93","y3_word94","y3_word95","y3_word96","y3_word97","y3_word98","y3_word99","y3_word100",
            "y3_word101","y3_word102","y3_word103","y3_word104","y3_word105","y3_word106","y3_word107","y3_word108","y3_word109","y3_word110",
            "y3_word111","y3_word112","y3_word113","y3_word114","y3_word115","y3_word116","y3_word117","y3_word118","y3_word119","y3_word120",
            "y3_word121","y3_word122","y3_word123","y3_word124","y3_word125","y3_word126","y3_word127","y3_word128","y3_word129","y3_word130",
            "y3_word131","y3_word132","y3_word133","y3_word134","y3_word135","y3_word136","y3_word137","y3_word138","y3_word139","y3_word140",
            "y3_word141","y3_word142","y3_word143","y3_word144","y3_word145","y3_word146","y3_word147","y3_word148","y3_word149","y3_word150",
            "y3_word151","y3_word152","y3_word153","y3_word154","y3_word155","y3_word156","y3_word157","y3_word158","y3_word159","y3_word160",
            "y3_word161","y3_word162","y3_word163","y3_word164","y3_word165","y3_word166","y3_word167","y3_word168","y3_word169","y3_word170",
            "y3_word171","y3_word172","y3_word173","y3_word174","y3_word175","y3_word176","y3_word177","y3_word178","y3_word179","y3_word180",
            "y3_word181","y3_word182","y3_word183","y3_word184","y3_word185","y3_word186","y3_word187","y3_word188","y3_word189","y3_word190",
            "y3_word191","y3_word192","y3_word193","y3_word194","y3_word195","y3_word196","y3_word197","y3_word198","y3_word199","y3_word200",
            "y3_word201","y3_word202","y3_word203","y3_word204","y3_word205","y3_word206","y3_word207","y3_word208","y3_word209","y3_word210",
            "y3_word211","y3_word212","y3_word213","y3_word214","y3_word215","y3_word216","y3_word217","y3_word218","y3_word219","y3_word220",
            "y3_word221","y3_word222","y3_word223","y3_word224","y3_word225","y3_word226","y3_word227","y3_word228","y3_word229","y3_word230",
            "y3_word231","y3_word232","y3_word233","y3_word234","y3_word235","y3_word236","y3_word237","y3_word238","y3_word239","y3_word240",
            "y3_word241","y3_word242","y3_word243","y3_word244","y3_word245","y3_word246","y3_word247","y3_word248","y3_word249","y3_word250",
            "y3_word251","y3_word252","y3_word253","y3_word254","y3_word255","y3_word256","y3_word257","y3_word258","y3_word259","y3_word260",
            "y3_word261","y3_word262","y3_word263","y3_word264","y3_word265","y3_word266","y3_word267","y3_word268","y3_word269","y3_word270",
            "y3_word271","y3_word272","y3_word273","y3_word274","y3_word275","y3_word276","y3_word277","y3_word278","y3_word279","y3_word280",
            "y3_word281","y3_word282","y3_word283","y3_word284","y3_word285","y3_word286","y3_word287","y3_word288","y3_word289","y3_word290",
            "y3_word291","y3_word292","y3_word293","y3_word294","y3_word295","y3_word296","y3_word297","y3_word298","y3_word299","y3_word300",
            "y3_word301","y3_word302","y3_word303","y3_word304","y3_word305","y3_word306","y3_word307","y3_word308","y3_word309","y3_word310",
            "y3_word311","y3_word312","y3_word313","y3_word314","y3_word315","y3_word316","y3_word317","y3_word318","y3_word319","y3_word320",
            "y3_word321","y3_word322","y3_word323","y3_word324","y3_word325","y3_word326","y3_word327","y3_word328","y3_word329","y3_word330",
            "y3_word331","y3_word332","y3_word333","y3_word334","y3_word335","y3_word336","y3_word337","y3_word338","y3_word339","y3_word340",
            "y3_word341","y3_word342","y3_word343","y3_word344","y3_word345","y3_word346","y3_word347","y3_word348","y3_word349","y3_word350",
            "y3_word351","y3_word352","y3_word353","y3_word354","y3_word355","y3_word356","y3_word357","y3_word358","y3_word359","y3_word360",
            "y3_word361","y3_word362","y3_word363","y3_word364","y3_word365","y3_word366","y3_word367","y3_word368","y3_word369","y3_word370",
            "y3_word371","y3_word372","y3_word373","y3_word374","y3_word375","y3_word376","y3_word377","y3_word378","y3_word379","y3_word380",
            "y3_word381","y3_word382","y3_word383","y3_word384","y3_word385","y3_word386","y3_word387","y3_word388","y3_word389","y3_word390",
            "y3_word391","y3_word392","y3_word393","y3_word394","y3_word395","y3_word396","y3_word397","y3_word398","y3_word399","y3_word400"
        };
        return words;
    }

    private string[] yasar1()
    {
        string[] words = new string[]
        {
            "y1_word1","y1_word2","y1_word3","y1_word4","y1_word5","y1_word6","y1_word7","y1_word8","y1_word9","y1_word10",
            "y1_word11","y1_word12","y1_word13","y1_word14","y1_word15","y1_word16","y1_word17","y1_word18","y1_word19","y1_word20",
            "y1_word21","y1_word22","y1_word23","y1_word24","y1_word25","y1_word26","y1_word27","y1_word28","y1_word29","y1_word30",
            "y1_word31","y1_word32","y1_word33","y1_word34","y1_word35","y1_word36","y1_word37","y1_word38","y1_word39","y1_word40",
            "y1_word41","y1_word42","y1_word43","y1_word44","y1_word45","y1_word46","y1_word47","y1_word48","y1_word49","y1_word50",
            "y1_word51","y1_word52","y1_word53","y1_word54","y1_word55","y1_word56","y1_word57","y1_word58","y1_word59","y1_word60",
            "y1_word61","y1_word62","y1_word63","y1_word64","y1_word65","y1_word66","y1_word67","y1_word68","y1_word69","y1_word70",
            "y1_word71","y1_word72","y1_word73","y1_word74","y1_word75","y1_word76","y1_word77","y1_word78","y1_word79","y1_word80",
            "y1_word81","y1_word82","y1_word83","y1_word84","y1_word85","y1_word86","y1_word87","y1_word88","y1_word89","y1_word90",
            "y1_word91","y1_word92","y1_word93","y1_word94","y1_word95","y1_word96","y1_word97","y1_word98","y1_word99","y1_word100",
            "y1_word101","y1_word102","y1_word103","y1_word104","y1_word105","y1_word106","y1_word107","y1_word108","y1_word109","y1_word110",
            "y1_word111","y1_word112","y1_word113","y1_word114","y1_word115","y1_word116","y1_word117","y1_word118","y1_word119","y1_word120",
            "y1_word121","y1_word122","y1_word123","y1_word124","y1_word125","y1_word126","y1_word127","y1_word128","y1_word129","y1_word130",
            "y1_word131","y1_word132","y1_word133","y1_word134","y1_word135","y1_word136","y1_word137","y1_word138","y1_word139","y1_word140",
            "y1_word141","y1_word142","y1_word143","y1_word144","y1_word145","y1_word146","y1_word147","y1_word148","y1_word149","y1_word150",
            "y1_word151","y1_word152","y1_word153","y1_word154","y1_word155","y1_word156","y1_word157","y1_word158","y1_word159","y1_word160",
            "y1_word161","y1_word162","y1_word163","y1_word164","y1_word165","y1_word166","y1_word167","y1_word168","y1_word169","y1_word170",
            "y1_word171","y1_word172","y1_word173","y1_word174","y1_word175","y1_word176","y1_word177","y1_word178","y1_word179","y1_word180",
            "y1_word181","y1_word182","y1_word183","y1_word184","y1_word185","y1_word186","y1_word187","y1_word188","y1_word189","y1_word190",
            "y1_word191","y1_word192","y1_word193","y1_word194","y1_word195","y1_word196","y1_word197","y1_word198","y1_word199","y1_word200",
            "y1_word201","y1_word202","y1_word203","y1_word204","y1_word205","y1_word206","y1_word207","y1_word208","y1_word209","y1_word210",
            "y1_word211","y1_word212","y1_word213","y1_word214","y1_word215","y1_word216","y1_word217","y1_word218","y1_word219","y1_word220",
            "y1_word221","y1_word222","y1_word223","y1_word224","y1_word225","y1_word226","y1_word227","y1_word228","y1_word229","y1_word230",
            "y1_word231","y1_word232","y1_word233","y1_word234","y1_word235","y1_word236","y1_word237","y1_word238","y1_word239","y1_word240",
            "y1_word241","y1_word242","y1_word243","y1_word244","y1_word245","y1_word246","y1_word247","y1_word248","y1_word249","y1_word250",
            "y1_word251","y1_word252","y1_word253","y1_word254","y1_word255","y1_word256","y1_word257","y1_word258","y1_word259","y1_word260",
            "y1_word261","y1_word262","y1_word263","y1_word264","y1_word265","y1_word266","y1_word267","y1_word268","y1_word269","y1_word270",
            "y1_word271","y1_word272","y1_word273","y1_word274","y1_word275","y1_word276","y1_word277","y1_word278","y1_word279","y1_word280",
            "y1_word281","y1_word282","y1_word283","y1_word284","y1_word285","y1_word286","y1_word287","y1_word288","y1_word289","y1_word290",
            "y1_word291","y1_word292","y1_word293","y1_word294","y1_word295","y1_word296","y1_word297","y1_word298","y1_word299","y1_word300",
            "y1_word301","y1_word302","y1_word303","y1_word304","y1_word305","y1_word306","y1_word307","y1_word308","y1_word309","y1_word310",
            "y1_word311","y1_word312","y1_word313","y1_word314","y1_word315","y1_word316","y1_word317","y1_word318","y1_word319","y1_word320",
            "y1_word321","y1_word322","y1_word323","y1_word324","y1_word325","y1_word326","y1_word327","y1_word328","y1_word329","y1_word330",
            "y1_word331","y1_word332","y1_word333","y1_word334","y1_word335","y1_word336","y1_word337","y1_word338","y1_word339","y1_word340",
            "y1_word341","y1_word342","y1_word343","y1_word344","y1_word345","y1_word346","y1_word347","y1_word348","y1_word349","y1_word350",
            "y1_word351","y1_word352","y1_word353","y1_word354","y1_word355","y1_word356","y1_word357","y1_word358","y1_word359","y1_word360",
            "y1_word361","y1_word362","y1_word363","y1_word364","y1_word365","y1_word366","y1_word367","y1_word368","y1_word369","y1_word370",
            "y1_word371","y1_word372","y1_word373","y1_word374","y1_word375","y1_word376","y1_word377","y1_word378","y1_word379","y1_word380",
            "y1_word381","y1_word382","y1_word383","y1_word384","y1_word385","y1_word386","y1_word387","y1_word388","y1_word389","y1_word390",
            "y1_word391","y1_word392","y1_word393","y1_word394","y1_word395","y1_word396","y1_word397","y1_word398","y1_word399","y1_word400"
        };
        return words;
    }



    private string[] yasar5()
    {
        string[] words = new string[]
        {
            "abandon", "ability", "absence", "absolute", "abstract", "academic", "accepted", "accident", "accompany", "according",
            "account", "accurate", "achieve", "acquire", "actually", "addition", "adequate", "adjacent", "adjust", "administration",
            "admit", "adolescent", "advance", "advanced", "advantage", "adventure", "adverse", "advertise", "advice", "advocate",
            "aesthetic", "affect", "afford", "afraid", "African", "afternoon", "against", "agency", "agenda", "agent",
            "aggressive", "agree", "agreement", "agricultural", "ahead", "aircraft", "airline", "airport", "album", "alcohol",
            "alert", "alien", "align", "alive", "alliance", "allow", "almost", "alone", "along", "already",
            "also", "alter", "alternative", "although", "altogether", "always", "amazing", "ambition", "ambulance", "American",
            "among", "amount", "analysis", "analyst", "analyze", "ancient", "anger", "angle", "angry", "animal",
            "anniversary", "announce", "annual", "another", "answer", "anticipate", "anxiety", "anxious", "anybody", "anymore",
            "anyone", "anything", "anyway", "anywhere", "apart", "apartment", "apparent", "apparently", "appeal", "appear",
            "appearance", "apple", "application", "apply", "appoint", "appointment", "appreciate", "approach", "appropriate", "approval",
            "approve", "approximately", "architect", "area", "argue", "argument", "arise", "armed", "army", "around",
            "arrange", "arrangement", "arrest", "arrival", "arrive", "article", "artist", "artistic", "aside", "aspect",
            "assault", "assert", "assess", "assessment", "asset", "assign", "assignment", "assist", "assistance", "assistant",
            "associate", "association", "assume", "assumption", "assure", "athlete", "athletic", "atmosphere", "attach", "attack",
            "attempt", "attend", "attention", "attitude", "attorney", "attract", "attractive", "attribute", "audience", "author",
            "authority", "auto", "automatic", "automobile", "automotive", "autumn", "available", "average", "avoid", "award",
            "aware", "away", "baby", "back", "background", "backup", "backward", "bacteria", "badly", "balance",
            "ball", "ban", "band", "bank", "bar", "bare", "barely", "bargain", "barrel", "barrier",
            "base", "baseball", "basic", "basically", "basin", "basis", "basket", "basketball", "bathroom", "battery",
            "battle", "beach", "beam", "bean", "bear", "beat", "beautiful", "beauty", "because", "become",
            "bedroom", "beer", "before", "begin", "beginning", "behavior", "behind", "being", "belief", "believe",
            "bell", "belong", "below", "belt", "bench", "bend", "beneath", "benefit", "beside", "best",
            "better", "between", "beyond", "bicycle", "bike", "bill", "billion", "bind", "biological", "bird",
            "birth", "birthday", "bite", "black", "blade", "blame", "blanket", "blind", "block", "blood",
            "blow", "blue", "board", "boat", "body", "bomb", "bond", "bone", "book", "boom",
            "boot", "border", "born", "borrow", "boss", "both", "bother", "bottle", "bottom", "boundary",
            "bowl", "brain", "branch", "brand", "brave", "bread", "break", "breakfast", "breast", "breath",
            "breathe", "breed", "brick", "bridge", "brief", "bright", "brilliant", "bring", "broad", "broadcast",
            "broken", "brother", "brown", "brush", "bucket", "buddy", "budget", "build", "building", "bullet",
            "bunch", "burden", "burn", "burst", "business", "button", "buyer", "cabinet", "cable", "cake",
            "calculate", "call", "camera", "camp", "campaign", "campus", "cancel", "cancer", "candidate", "capacity",
            "capital", "captain", "capture", "carbon", "card", "care", "career", "careful", "carefully", "carry",
            "case", "cash", "cast", "catch", "category", "cause", "ceiling", "celebrate", "celebration", "celebrity",
            "cell", "center", "central", "century", "ceremony", "certain", "certainly", "chain", "chair", "chairman",
            "challenge", "chamber", "champion", "championship", "chance", "change", "channel", "chapter", "character", "characteristic",
            "characterize", "charge", "charity", "chart", "chase", "cheap", "check", "cheese", "chemical", "chest",
            "chicken", "chief", "child", "childhood", "chip", "chocolate", "choice", "choose", "church", "cigarette",
            "circle", "circumstance", "citizen", "city", "civil", "civilian", "claim", "class", "classic", "classroom",
            "clean", "clear", "clearly", "client", "climate", "climb", "clinic", "clock", "close", "closely",
            "closer", "clothes", "clothing", "cloud", "club", "clue", "coach", "coal", "coast", "coat",
            "code", "coffee", "cognitive", "cold", "collapse", "colleague", "collect", "collection", "collective", "college",
            "colonial", "color", "column", "combination", "combine", "come", "comedy", "comfort", "comfortable", "command",
            "comment", "commercial", "commission", "commit", "commitment", "committee", "common", "communicate", "communication", "community"
        };
        return words;
    }

    private string[] yasarv1()
    {
        string[] words = new string[]
        {
            "vehicle", "version", "versus", "vertical", "very", "veteran", "victim", "victory", "video", "view",
            "village", "violent", "virtual", "virus", "visible", "vision", "visit", "visual", "vital", "vitamin",
            "voice", "volume", "volunteer", "vote", "voyage", "wage", "wait", "wake", "walk", "wall",
            "want", "war", "warm", "warn", "wash", "waste", "watch", "water", "wave", "way",
            "we", "weak", "wealth", "weapon", "wear", "weather", "web", "website", "wedding", "week",
            "weekend", "weekly", "weight", "welcome", "welfare", "well", "west", "western", "what", "whatever",
            "wheel", "when", "whenever", "where", "whereas", "whereby", "wherever", "whether", "which", "while",
            "white", "who", "whoever", "whole", "whom", "whose", "why", "wide", "widely", "wife",
            "wild", "will", "willing", "win", "wind", "window", "wine", "wing", "winner", "winning",
            "winter", "wire", "wisdom", "wise", "wish", "with", "within", "without", "witness", "woman",
            "wonder", "wonderful", "wood", "wooden", "word", "work", "worker", "working", "workplace", "workshop",
            "world", "worldwide", "worn", "worried", "worry", "worse", "worship", "worst", "worth", "would",
            "wound", "wrap", "write", "writer", "writing", "written", "wrong", "yard", "yeah", "year",
            "yellow", "yes", "yesterday", "yet", "yield", "young", "younger", "your", "yours", "yourself",
            "youth", "zone", "machine", "magic", "magnetic", "magnificent", "mail", "main", "mainly", "maintain",
            "maintenance", "major", "majority", "make", "maker", "makeup", "male", "mall", "man", "manage",
            "management", "manager", "manner", "manufacturing", "many", "map", "march", "margin", "mark", "market",
            "marketing", "marriage", "married", "marry", "mask", "mass", "massive", "master", "match", "material",
            "math", "matter", "maximum", "may", "maybe", "mayor", "meal", "mean", "meaning", "meanwhile",
            "measure", "measurement", "meat", "mechanism", "media", "medical", "medication", "medicine", "medium", "meet",
            "meeting", "member", "membership", "memory", "mental", "mention", "menu", "mere", "merely", "mess",
            "message", "metal", "method", "middle", "might", "military", "milk", "mind", "mine", "mineral",
            "minimum", "mining", "minor", "minority", "minute", "miracle", "mirror", "miss", "missile", "mission",
            "mistake", "mix", "mixture", "mobile", "mode", "model", "moderate", "modern", "modest", "mom",
            "moment", "money", "monitor", "month", "monthly", "mood", "moon", "moral", "more", "morning",
            "mortgage", "most", "mostly", "mother", "motion", "motor", "mount", "mountain", "mouse", "mouth",
            "move", "movement", "movie", "much", "multiple", "murder", "muscle", "museum", "music", "musical",
            "musician", "must", "mutual", "myself", "mystery", "myth", "naked", "name", "narrative", "narrow",
            "nation", "national", "native", "natural", "naturally", "nature", "navy", "near", "nearby", "nearly",
            "necessarily", "necessary", "neck", "need", "negative", "negotiate", "negotiation", "neighbor", "neighborhood", "neither",
            "nerve", "nervous", "nest", "net", "network", "neutral", "never", "nevertheless", "new", "newly",
            "news", "newspaper", "next", "nice", "night", "nine", "no", "nobody", "nod", "noise",
            "none", "nonetheless", "noon", "nor", "normal", "normally", "north", "northern", "nose", "not",
            "notable", "note", "nothing", "notice", "notion", "novel", "now", "nowhere", "nuclear", "number",
            "numerous", "nurse", "nut", "object", "objective", "obligation", "observation", "observe", "observer", "obtain",
            "obvious", "obviously", "occasion", "occasionally", "occur", "ocean", "odd", "odds", "of", "off",
            "offense", "offensive", "offer", "office", "officer", "official", "often", "oil", "okay", "old",
            "Olympic", "on", "once", "one", "ongoing", "onion", "online", "only", "onto", "open",
            "opening", "operate", "operating", "operation", "operator", "opinion", "opponent", "opportunity", "oppose", "opposite",
            "opposition", "option", "or", "orange", "order", "ordinary", "organic", "organization", "organize", "orientation",
            "origin", "original", "originally", "other", "otherwise", "ought", "our", "ourselves", "out", "outcome",
            "outside", "overall", "overcome", "overlook", "owe", "own", "owner", "pace", "pack", "package",
            "page", "pain", "painful", "paint", "painting", "pair", "pale", "pan", "panel", "paper",
            "parent", "park", "parking", "part", "participant", "participate", "participation", "particular", "particularly", "partly",
            "partner", "partnership", "party", "pass", "passage", "passenger", "passion", "past", "patch", "path",
            "patient", "pattern", "pause", "pay", "payment", "peace", "peak", "peer", "penalty", "people"
        };
        return words;
    }

    private string[] yasarv2()
    {
        string[] words = new string[]
        {
            "perfect", "perform", "performance", "perhaps", "period", "permanent", "permission", "permit", "person", "personal",
            "personality", "personally", "personnel", "perspective", "phase", "phenomenon", "philosophy", "phone", "photo", "photograph",
            "photographer", "photography", "physical", "physically", "physician", "piano", "pick", "picture", "piece", "pile",
            "pilot", "pin", "pink", "pipe", "pitch", "place", "plan", "plane", "planet", "planning",
            "plant", "plastic", "plate", "platform", "play", "player", "playing", "plaza", "pleasant", "please",
            "pleasure", "plenty", "plot", "plus", "pocket", "poem", "poet", "poetry", "point", "pole",
            "police", "policy", "political", "politically", "politician", "politics", "poll", "pollution", "pool", "poor",
            "pop", "popular", "popularity", "population", "port", "portion", "portrait", "portray", "pose", "position",
            "positive", "possess", "possession", "possibility", "possible", "possibly", "post", "pot", "potato", "potential",
            "potentially", "pound", "pour", "poverty", "power", "powerful", "practical", "practice", "pray", "prayer",
            "precise", "precisely", "predict", "prediction", "prefer", "preference", "pregnancy", "pregnant", "preparation", "prepare",
            "prescription", "presence", "present", "presentation", "preserve", "president", "presidential", "press", "pressure", "pretend",
            "pretty", "prevent", "prevention", "previous", "previously", "price", "pride", "priest", "primary", "prime",
            "principal", "principle", "print", "prior", "priority", "prison", "prisoner", "privacy", "private", "probably",
            "problem", "procedure", "proceed", "process", "produce", "producer", "product", "production", "profession", "professional",
            "professor", "profile", "profit", "program", "project", "prominent", "promise", "promote", "promotion", "prompt",
            "proof", "proper", "properly", "property", "proportion", "proposal", "propose", "proposed", "prosecutor", "prospect",
            "protect", "protection", "protein", "protest", "proud", "prove", "provide", "provider", "province", "provision",
            "psychological", "psychologist", "psychology", "public", "publication", "publicly", "publish", "publisher", "pull", "punishment",
            "purchase", "pure", "purpose", "pursue", "push", "put", "qualify", "quality", "quarter", "quarterback",
            "question", "quick", "quickly", "quiet", "quietly", "quit", "quite", "quote", "race", "racial",
            "radio", "rail", "railroad", "rain", "raise", "range", "rank", "rapid", "rapidly", "rare",
            "rarely", "rate", "rather", "rating", "ratio", "raw", "reach", "react", "reaction", "read",
            "reader", "reading", "ready", "real", "reality", "realize", "really", "reason", "reasonable", "recall",
            "receive", "recent", "recently", "recognition", "recognize", "recommend", "recommendation", "record", "recover", "recovery",
            "recruit", "red", "reduce", "reduction", "refer", "reference", "reflect", "reflection", "reform", "refuse",
            "regard", "regarding", "regardless", "region", "regional", "regular", "regularly", "regulation", "reject", "relate",
            "relation", "relationship", "relative", "relatively", "relax", "release", "relevant", "reliable", "relief", "religion",
            "religious", "reluctant", "rely", "remain", "remaining", "remarkable", "remember", "remind", "remove", "repeat",
            "repeatedly", "replace", "reply", "report", "reporter", "represent", "representation", "representative", "reputation", "request",
            "require", "requirement", "rescue", "research", "researcher", "resemble", "reservation", "reserve", "resident", "resign",
            "resistance", "resolution", "resolve", "resource", "respect", "respond", "response", "responsibility", "responsible", "rest",
            "restaurant", "restore", "restriction", "result", "retail", "retain", "retire", "retirement", "return", "reveal",
            "revenue", "review", "revolution", "reward", "rich", "rid", "ride", "rider", "rifle", "right",
            "ring", "rise", "risk", "river", "road", "rob", "rock", "role", "roll", "romantic",
            "roof", "room", "root", "rope", "rose", "rotate", "rough", "roughly", "round", "route",
            "routine", "row", "rub", "rule", "ruler", "run", "running", "rural", "rush", "Russian",
            "sacred", "sad", "safe", "safety", "sake", "salad", "salary", "sale", "sales", "salt",
            "same", "sample", "sanction", "sand", "satellite", "satisfaction", "satisfy", "sauce", "save", "saving",
            "say", "scale", "scandal", "scared", "scenario", "scene", "schedule", "scheme", "scholar", "scholarship",
            "school", "science", "scientific", "scientist", "scope", "score", "scream", "screen", "script", "sea",
            "search", "season", "seat", "second", "secondary", "secret", "secretary", "section", "sector", "secure",
            "security", "see", "seed", "seek", "seem", "select", "selection", "self", "sell", "senate",
            "senator", "send", "senior", "sense", "sensitive", "sentence", "separate", "sequence", "series", "serious",
            "seriously", "serve", "service", "session", "set", "setting", "settle", "settlement", "seven", "several",
            "severe", "sex", "sexual", "shade", "shadow", "shake", "shall", "shame", "shape", "share",
            "sharp", "she", "sheet", "shelf", "shell", "shelter", "shift", "shine", "ship", "shirt",
            "shock", "shoe", "shoot", "shooting", "shop", "shopping", "shore", "short", "shot", "should",
            "shoulder", "shout", "show", "shower", "shut", "sick", "side", "sight", "sign", "signal",
            "significant", "significantly", "silence", "silent", "silver", "similar", "similarly", "simple", "simply", "sin"
        };
        return words;
    }
    private string[] baha3()
    {
        string[] words = new string[]
        {
            "baha3_word1", "baha3_word2", "baha3_word3", "baha3_word4", "baha3_word5", "baha3_word6", "baha3_word7", "baha3_word8", "baha3_word9", "baha3_word10",
            "baha3_word11", "baha3_word12", "baha3_word13", "baha3_word14", "baha3_word15", "baha3_word16", "baha3_word17", "baha3_word18", "baha3_word19", "baha3_word20",
            "baha3_word21", "baha3_word22", "baha3_word23", "baha3_word24", "baha3_word25", "baha3_word26", "baha3_word27", "baha3_word28", "baha3_word29", "baha3_word30",
            "baha3_word31", "baha3_word32", "baha3_word33", "baha3_word34", "baha3_word35", "baha3_word36", "baha3_word37", "baha3_word38", "baha3_word39", "baha3_word40",
            "baha3_word41", "baha3_word42", "baha3_word43", "baha3_word44", "baha3_word45", "baha3_word46", "baha3_word47", "baha3_word48", "baha3_word49", "baha3_word50",
            "baha3_word51", "baha3_word52", "baha3_word53", "baha3_word54", "baha3_word55", "baha3_word56", "baha3_word57", "baha3_word58", "baha3_word59", "baha3_word60",
            "baha3_word61", "baha3_word62", "baha3_word63", "baha3_word64", "baha3_word65", "baha3_word66", "baha3_word67", "baha3_word68", "baha3_word69", "baha3_word70",
            "baha3_word71", "baha3_word72", "baha3_word73", "baha3_word74", "baha3_word75", "baha3_word76", "baha3_word77", "baha3_word78", "baha3_word79", "baha3_word80",
            "baha3_word81", "baha3_word82", "baha3_word83", "baha3_word84", "baha3_word85", "baha3_word86", "baha3_word87", "baha3_word88", "baha3_word89", "baha3_word90",
            "baha3_word91", "baha3_word92", "baha3_word93", "baha3_word94", "baha3_word95", "baha3_word96", "baha3_word97", "baha3_word98", "baha3_word99", "baha3_word100",
            "baha3_word101", "baha3_word102", "baha3_word103", "baha3_word104", "baha3_word105", "baha3_word106", "baha3_word107", "baha3_word108", "baha3_word109", "baha3_word110",
            "baha3_word111", "baha3_word112", "baha3_word113", "baha3_word114", "baha3_word115", "baha3_word116", "baha3_word117", "baha3_word118", "baha3_word119", "baha3_word120",
            "baha3_word121", "baha3_word122", "baha3_word123", "baha3_word124", "baha3_word125", "baha3_word126", "baha3_word127", "baha3_word128", "baha3_word129", "baha3_word130",
            "baha3_word131", "baha3_word132", "baha3_word133", "baha3_word134", "baha3_word135", "baha3_word136", "baha3_word137", "baha3_word138", "baha3_word139", "baha3_word140",
            "baha3_word141", "baha3_word142", "baha3_word143", "baha3_word144", "baha3_word145", "baha3_word146", "baha3_word147", "baha3_word148", "baha3_word149", "baha3_word150",
            "baha3_word151", "baha3_word152", "baha3_word153", "baha3_word154", "baha3_word155", "baha3_word156", "baha3_word157", "baha3_word158", "baha3_word159", "baha3_word160",
            "baha3_word161", "baha3_word162", "baha3_word163", "baha3_word164", "baha3_word165", "baha3_word166", "baha3_word167", "baha3_word168", "baha3_word169", "baha3_word170",
            "baha3_word171", "baha3_word172", "baha3_word173", "baha3_word174", "baha3_word175", "baha3_word176", "baha3_word177", "baha3_word178", "baha3_word179", "baha3_word180",
            "baha3_word181", "baha3_word182", "baha3_word183", "baha3_word184", "baha3_word185", "baha3_word186", "baha3_word187", "baha3_word188", "baha3_word189", "baha3_word190",
            "baha3_word191", "baha3_word192", "baha3_word193", "baha3_word194", "baha3_word195", "baha3_word196", "baha3_word197", "baha3_word198", "baha3_word199", "baha3_word200",
            "baha3_word201", "baha3_word202", "baha3_word203", "baha3_word204", "baha3_word205", "baha3_word206", "baha3_word207", "baha3_word208", "baha3_word209", "baha3_word210",
            "baha3_word211", "baha3_word212", "baha3_word213", "baha3_word214", "baha3_word215", "baha3_word216", "baha3_word217", "baha3_word218", "baha3_word219", "baha3_word220",
            "baha3_word221", "baha3_word222", "baha3_word223", "baha3_word224", "baha3_word225", "baha3_word226", "baha3_word227", "baha3_word228", "baha3_word229", "baha3_word230",
            "baha3_word231", "baha3_word232", "baha3_word233", "baha3_word234", "baha3_word235", "baha3_word236", "baha3_word237", "baha3_word238", "baha3_word239", "baha3_word240",
            "baha3_word241", "baha3_word242", "baha3_word243", "baha3_word244", "baha3_word245", "baha3_word246", "baha3_word247", "baha3_word248", "baha3_word249", "baha3_word250",
            "baha3_word251", "baha3_word252", "baha3_word253", "baha3_word254", "baha3_word255", "baha3_word256", "baha3_word257", "baha3_word258", "baha3_word259", "baha3_word260",
            "baha3_word261", "baha3_word262", "baha3_word263", "baha3_word264", "baha3_word265", "baha3_word266", "baha3_word267", "baha3_word268", "baha3_word269", "baha3_word270",
            "baha3_word271", "baha3_word272", "baha3_word273", "baha3_word274", "baha3_word275", "baha3_word276", "baha3_word277", "baha3_word278", "baha3_word279", "baha3_word280",
            "baha3_word281", "baha3_word282", "baha3_word283", "baha3_word284", "baha3_word285", "baha3_word286", "baha3_word287", "baha3_word288", "baha3_word289", "baha3_word290",
            "baha3_word291", "baha3_word292", "baha3_word293", "baha3_word294", "baha3_word295", "baha3_word296", "baha3_word297", "baha3_word298", "baha3_word299", "baha3_word300",
            "baha3_word301", "baha3_word302", "baha3_word303", "baha3_word304", "baha3_word305", "baha3_word306", "baha3_word307", "baha3_word308", "baha3_word309", "baha3_word310",
            "baha3_word311", "baha3_word312", "baha3_word313", "baha3_word314", "baha3_word315", "baha3_word316", "baha3_word317", "baha3_word318", "baha3_word319", "baha3_word320",
            "baha3_word321", "baha3_word322", "baha3_word323", "baha3_word324", "baha3_word325", "baha3_word326", "baha3_word327", "baha3_word328", "baha3_word329", "baha3_word330",
            "baha3_word331", "baha3_word332", "baha3_word333", "baha3_word334", "baha3_word335", "baha3_word336", "baha3_word337", "baha3_word338", "baha3_word339", "baha3_word340",
            "baha3_word341", "baha3_word342", "baha3_word343", "baha3_word344", "baha3_word345", "baha3_word346", "baha3_word347", "baha3_word348", "baha3_word349", "baha3_word350",
            "baha3_word351", "baha3_word352", "baha3_word353", "baha3_word354", "baha3_word355", "baha3_word356", "baha3_word357", "baha3_word358", "baha3_word359", "baha3_word360",
            "baha3_word361", "baha3_word362", "baha3_word363", "baha3_word364", "baha3_word365", "baha3_word366", "baha3_word367", "baha3_word368", "baha3_word369", "baha3_word370",
            "baha3_word371", "baha3_word372", "baha3_word373", "baha3_word374", "baha3_word375", "baha3_word376", "baha3_word377", "baha3_word378", "baha3_word379", "baha3_word380",
            "baha3_word381", "baha3_word382", "baha3_word383", "baha3_word384", "baha3_word385", "baha3_word386", "baha3_word387", "baha3_word388", "baha3_word389", "baha3_word390",
            "baha3_word391", "baha3_word392", "baha3_word393", "baha3_word394", "baha3_word395", "baha3_word396", "baha3_word397", "baha3_word398", "baha3_word399", "baha3_word400"
        };
        return words;
    }

    private string[] baha4()
    {
        string[] words = new string[]
        {
            "baha4_word1", "baha4_word2", "baha4_word3", "baha4_word4", "baha4_word5", "baha4_word6", "baha4_word7", "baha4_word8", "baha4_word9", "baha4_word10",
            "baha4_word11", "baha4_word12", "baha4_word13", "baha4_word14", "baha4_word15", "baha4_word16", "baha4_word17", "baha4_word18", "baha4_word19", "baha4_word20",
            "baha4_word21", "baha4_word22", "baha4_word23", "baha4_word24", "baha4_word25", "baha4_word26", "baha4_word27", "baha4_word28", "baha4_word29", "baha4_word30",
            "baha4_word31", "baha4_word32", "baha4_word33", "baha4_word34", "baha4_word35", "baha4_word36", "baha4_word37", "baha4_word38", "baha4_word39", "baha4_word40",
            "baha4_word41", "baha4_word42", "baha4_word43", "baha4_word44", "baha4_word45", "baha4_word46", "baha4_word47", "baha4_word48", "baha4_word49", "baha4_word50",
            "baha4_word51", "baha4_word52", "baha4_word53", "baha4_word54", "baha4_word55", "baha4_word56", "baha4_word57", "baha4_word58", "baha4_word59", "baha4_word60",
            "baha4_word61", "baha4_word62", "baha4_word63", "baha4_word64", "baha4_word65", "baha4_word66", "baha4_word67", "baha4_word68", "baha4_word69", "baha4_word70",
            "baha4_word71", "baha4_word72", "baha4_word73", "baha4_word74", "baha4_word75", "baha4_word76", "baha4_word77", "baha4_word78", "baha4_word79", "baha4_word80",
            "baha4_word81", "baha4_word82", "baha4_word83", "baha4_word84", "baha4_word85", "baha4_word86", "baha4_word87", "baha4_word88", "baha4_word89", "baha4_word90",
            "baha4_word91", "baha4_word92", "baha4_word93", "baha4_word94", "baha4_word95", "baha4_word96", "baha4_word97", "baha4_word98", "baha4_word99", "baha4_word100",
            "baha4_word101", "baha4_word102", "baha4_word103", "baha4_word104", "baha4_word105", "baha4_word106", "baha4_word107", "baha4_word108", "baha4_word109", "baha4_word110",
            "baha4_word111", "baha4_word112", "baha4_word113", "baha4_word114", "baha4_word115", "baha4_word116", "baha4_word117", "baha4_word118", "baha4_word119", "baha4_word120",
            "baha4_word121", "baha4_word122", "baha4_word123", "baha4_word124", "baha4_word125", "baha4_word126", "baha4_word127", "baha4_word128", "baha4_word129", "baha4_word130",
            "baha4_word131", "baha4_word132", "baha4_word133", "baha4_word134", "baha4_word135", "baha4_word136", "baha4_word137", "baha4_word138", "baha4_word139", "baha4_word140",
            "baha4_word141", "baha4_word142", "baha4_word143", "baha4_word144", "baha4_word145", "baha4_word146", "baha4_word147", "baha4_word148", "baha4_word149", "baha4_word150",
            "baha4_word151", "baha4_word152", "baha4_word153", "baha4_word154", "baha4_word155", "baha4_word156", "baha4_word157", "baha4_word158", "baha4_word159", "baha4_word160",
            "baha4_word161", "baha4_word162", "baha4_word163", "baha4_word164", "baha4_word165", "baha4_word166", "baha4_word167", "baha4_word168", "baha4_word169", "baha4_word170",
            "baha4_word171", "baha4_word172", "baha4_word173", "baha4_word174", "baha4_word175", "baha4_word176", "baha4_word177", "baha4_word178", "baha4_word179", "baha4_word180",
            "baha4_word181", "baha4_word182", "baha4_word183", "baha4_word184", "baha4_word185", "baha4_word186", "baha4_word187", "baha4_word188", "baha4_word189", "baha4_word190",
            "baha4_word191", "baha4_word192", "baha4_word193", "baha4_word194", "baha4_word195", "baha4_word196", "baha4_word197", "baha4_word198", "baha4_word199", "baha4_word200",
            "baha4_word201", "baha4_word202", "baha4_word203", "baha4_word204", "baha4_word205", "baha4_word206", "baha4_word207", "baha4_word208", "baha4_word209", "baha4_word210",
            "baha4_word211", "baha4_word212", "baha4_word213", "baha4_word214", "baha4_word215", "baha4_word216", "baha4_word217", "baha4_word218", "baha4_word219", "baha4_word220",
            "baha4_word221", "baha4_word222", "baha4_word223", "baha4_word224", "baha4_word225", "baha4_word226", "baha4_word227", "baha4_word228", "baha4_word229", "baha4_word230",
            "baha4_word231", "baha4_word232", "baha4_word233", "baha4_word234", "baha4_word235", "baha4_word236", "baha4_word237", "baha4_word238", "baha4_word239", "baha4_word240",
            "baha4_word241", "baha4_word242", "baha4_word243", "baha4_word244", "baha4_word245", "baha4_word246", "baha4_word247", "baha4_word248", "baha4_word249", "baha4_word250",
            "baha4_word251", "baha4_word252", "baha4_word253", "baha4_word254", "baha4_word255", "baha4_word256", "baha4_word257", "baha4_word258", "baha4_word259", "baha4_word260",
            "baha4_word261", "baha4_word262", "baha4_word263", "baha4_word264", "baha4_word265", "baha4_word266", "baha4_word267", "baha4_word268", "baha4_word269", "baha4_word270",
            "baha4_word271", "baha4_word272", "baha4_word273", "baha4_word274", "baha4_word275", "baha4_word276", "baha4_word277", "baha4_word278", "baha4_word279", "baha4_word280",
            "baha4_word281", "baha4_word282", "baha4_word283", "baha4_word284", "baha4_word285", "baha4_word286", "baha4_word287", "baha4_word288", "baha4_word289", "baha4_word290",
            "baha4_word291", "baha4_word292", "baha4_word293", "baha4_word294", "baha4_word295", "baha4_word296", "baha4_word297", "baha4_word298", "baha4_word299", "baha4_word300",
            "baha4_word301", "baha4_word302", "baha4_word303", "baha4_word304", "baha4_word305", "baha4_word306", "baha4_word307", "baha4_word308", "baha4_word309", "baha4_word310",
            "baha4_word311", "baha4_word312", "baha4_word313", "baha4_word314", "baha4_word315", "baha4_word316", "baha4_word317", "baha4_word318", "baha4_word319", "baha4_word320",
            "baha4_word321", "baha4_word322", "baha4_word323", "baha4_word324", "baha4_word325", "baha4_word326", "baha4_word327", "baha4_word328", "baha4_word329", "baha4_word330",
            "baha4_word331", "baha4_word332", "baha4_word333", "baha4_word334", "baha4_word335", "baha4_word336", "baha4_word337", "baha4_word338", "baha4_word339", "baha4_word340",
            "baha4_word341", "baha4_word342", "baha4_word343", "baha4_word344", "baha4_word345", "baha4_word346", "baha4_word347", "baha4_word348", "baha4_word349", "baha4_word350",
            "baha4_word351", "baha4_word352", "baha4_word353", "baha4_word354", "baha4_word355", "baha4_word356", "baha4_word357", "baha4_word358", "baha4_word359", "baha4_word360",
            "baha4_word361", "baha4_word362", "baha4_word363", "baha4_word364", "baha4_word365", "baha4_word366", "baha4_word367", "baha4_word368", "baha4_word369", "baha4_word370",
            "baha4_word371", "baha4_word372", "baha4_word373", "baha4_word374", "baha4_word375", "baha4_word376", "baha4_word377", "baha4_word378", "baha4_word379", "baha4_word380",
            "baha4_word381", "baha4_word382", "baha4_word383", "baha4_word384", "baha4_word385", "baha4_word386", "baha4_word387", "baha4_word388", "baha4_word389", "baha4_word390",
            "baha4_word391", "baha4_word392", "baha4_word393", "baha4_word394", "baha4_word395", "baha4_word396", "baha4_word397", "baha4_word398", "baha4_word399", "baha4_word400",
            "baha4_word401", "baha4_word402", "baha4_word403", "baha4_word404", "baha4_word405", "baha4_word406", "baha4_word407", "baha4_word408", "baha4_word409", "baha4_word410",
            "baha4_word411", "baha4_word412", "baha4_word413", "baha4_word414", "baha4_word415", "baha4_word416", "baha4_word417", "baha4_word418", "baha4_word419", "baha4_word420",
            "baha4_word421", "baha4_word422", "baha4_word423", "baha4_word424", "baha4_word425", "baha4_word426", "baha4_word427", "baha4_word428", "baha4_word429", "baha4_word430",
            "baha4_word431", "baha4_word432", "baha4_word433", "baha4_word434", "baha4_word435", "baha4_word436", "baha4_word437", "baha4_word438", "baha4_word439", "baha4_word440",
            "baha4_word441", "baha4_word442", "baha4_word443", "baha4_word444", "baha4_word445", "baha4_word446", "baha4_word447", "baha4_word448", "baha4_word449", "baha4_word450",
            "baha4_word451", "baha4_word452", "baha4_word453", "baha4_word454", "baha4_word455", "baha4_word456", "baha4_word457", "baha4_word458", "baha4_word459", "baha4_word460",
            "baha4_word461", "baha4_word462", "baha4_word463", "baha4_word464", "baha4_word465", "baha4_word466", "baha4_word467", "baha4_word468", "baha4_word469", "baha4_word470",
            "baha4_word471", "baha4_word472", "baha4_word473", "baha4_word474", "baha4_word475", "baha4_word476", "baha4_word477", "baha4_word478", "baha4_word479", "baha4_word480",
            "baha4_word481", "baha4_word482", "baha4_word483", "baha4_word484", "baha4_word485", "baha4_word486", "baha4_word487", "baha4_word488", "baha4_word489", "baha4_word490",
            "baha4_word491", "baha4_word492", "baha4_word493", "baha4_word494", "baha4_word495", "baha4_word496", "baha4_word497", "baha4_word498", "baha4_word499", "baha4_word500"
        };
        return words;
    }

    private void btnDeneme13_Click(object sender, EventArgs e)
    {
        string[] words = baha4();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < words.Length; i++)
        {
            sb.Append(words[i]);
            if (i < words.Length - 1)
                sb.Append(" ");
        }
        string message = sb.ToString();
        int tokenCount = message.Length / 4;
        int charCount = message.Length;
        int wordCount = words.Length;
        MessageBox.Show($"Number of tokens: {tokenCount}\nNumber of words: {wordCount}\nNumber of characters: {charCount}\n\n{message}");
    }

    private void btnDeneme15_Click(object sender, EventArgs e)
    {
        string[] words = baha4();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < words.Length; i++)
        {
            sb.Append(words[i]);
            if (i < words.Length - 1)
                sb.Append(" ");
        }
        string message = sb.ToString();
        int tokenCount = message.Length / 4;
        int charCount = message.Length;
        int wordCount = words.Length;
        MessageBox.Show($"Number of tokens: {tokenCount}\nNumber of words: {wordCount}\nNumber of characters: {charCount}\n\n{message}");
    }
    
    private string[] baha5()
    {
        List<string> words = new List<string>();
        for (int i = 1; i <= 400; i++)
        {
            words.Add("baha5_word" + i);
        }
        return words.ToArray();
    }
    private string[] baha6()
    {
        List<string> words = new List<string>();
        for (int i = 1; i <= 400; i++)
        {
            words.Add("baha6_word" + i);
        }
        return words.ToArray();
    } // baha6 metodunun kapanış parantezi
    
    private string[] baha8()
    {
        List<string> words = new List<string>();
        for (int i = 1; i <= 400; i++)
        {
            words.Add("baha8_word" + i);
        }
        return words.ToArray();
    }
    
    private void btnDeneme10_Click(object sender, EventArgs e)
    {
        string[] words = baha3();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < words.Length; i++)
        {
            sb.Append(words[i]);
            if (i < words.Length - 1)
                sb.Append(" ");
        }
        string message = sb.ToString();
        int tokenCount = message.Length / 4;
        int charCount = message.Length;
        int wordCount = words.Length;
        MessageBox.Show($"Number of tokens: {tokenCount}\nNumber of words: {wordCount}\nNumber of characters: {charCount}\n\n{message}");
    }

    private void btnDeneme11_Click(object sender, EventArgs e)
    {
        string[] words = baha4();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < words.Length; i++)
        {
            sb.Append(words[i]);
            if (i < words.Length - 1)
                sb.Append(" ");
        }
        string message = sb.ToString();
        int tokenCount = message.Length / 4;
        int charCount = message.Length;
        int wordCount = words.Length;
        MessageBox.Show($"Number of tokens: {tokenCount}\nNumber of words: {wordCount}\nNumber of characters: {charCount}\n\n{message}");
    }

    private void btnDeneme12_Click(object sender, EventArgs e)
    {
        string[] words = baha4();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < words.Length; i++)
        {
            sb.Append(words[i]);
            if (i < words.Length - 1)
                sb.Append(" ");
        }
        string message = sb.ToString();
        int tokenCount = message.Length / 4;
        int charCount = message.Length;
        int wordCount = words.Length;
        MessageBox.Show($"Number of tokens: {tokenCount}\nNumber of words: {wordCount}\nNumber of characters: {charCount}\n\n{message}");
    }
    
    private string[] bahav1()
    {
        string[] words = new string[]
        {
            // Animals (50 words)
            "elephant", "giraffe", "leopard", "cheetah", "rhinoceros", "hippopotamus", "crocodile", "alligator", "penguin", "flamingo",
            "kangaroo", "koala", "panda", "chimpanzee", "gorilla", "orangutan", "dolphin", "whale", "shark", "octopus",
            "butterfly", "dragonfly", "grasshopper", "beetle", "spider", "scorpion", "eagle", "falcon", "hawk", "owl",
            "parrot", "peacock", "swan", "heron", "pelican", "seagull", "robin", "sparrow", "crow", "raven",
            "wolf", "fox", "bear", "deer", "rabbit", "squirrel", "hedgehog", "raccoon", "skunk", "opossum",

            // Colors & Descriptions (50 words)
            "crimson", "scarlet", "burgundy", "maroon", "coral", "salmon", "peach", "apricot", "amber", "golden",
            "emerald", "jade", "olive", "lime", "mint", "sage", "forest", "navy", "azure", "cobalt",
            "indigo", "violet", "lavender", "magenta", "fuchsia", "rose", "pink", "beige", "tan", "brown",
            "chocolate", "coffee", "cream", "ivory", "pearl", "silver", "platinum", "steel", "charcoal", "ebony",
            "bright", "vivid", "pale", "dark", "light", "deep", "rich", "soft", "muted", "vibrant",

            // Food & Cooking (50 words)
            "artichoke", "asparagus", "broccoli", "cauliflower", "spinach", "lettuce", "cabbage", "celery", "cucumber", "zucchini",
            "eggplant", "pepper", "tomato", "potato", "carrot", "onion", "garlic", "ginger", "basil", "oregano",
            "thyme", "rosemary", "parsley", "cilantro", "mint", "sage", "cinnamon", "nutmeg", "vanilla", "chocolate",
            "strawberry", "blueberry", "raspberry", "blackberry", "cranberry", "grape", "apple", "orange", "banana", "mango",
            "pineapple", "coconut", "almond", "walnut", "pecan", "cashew", "pistachio", "hazelnut", "recipe", "ingredient",

            // Technology & Science (50 words)
            "algorithm", "database", "network", "server", "client", "protocol", "encryption", "security", "firewall", "router",
            "processor", "memory", "storage", "bandwidth", "latency", "throughput", "interface", "framework", "library", "module",
            "function", "variable", "parameter", "argument", "syntax", "debugging", "compilation", "execution", "optimization", "performance",
            "microscope", "telescope", "laboratory", "experiment", "hypothesis", "theory", "research", "analysis", "discovery", "invention",
            "chemistry", "physics", "biology", "astronomy", "geology", "mathematics", "engineering", "medicine", "genetics", "molecule",

            // Nature & Weather (50 words)
            "mountain", "valley", "canyon", "plateau", "cliff", "cave", "waterfall", "river", "stream", "lake",
            "ocean", "beach", "island", "peninsula", "volcano", "earthquake", "glacier", "desert", "forest", "jungle",
            "meadow", "prairie", "tundra", "swamp", "marsh", "reef", "tide", "wave", "current", "whirlpool",
            "thunder", "lightning", "rainbow", "sunset", "sunrise", "twilight", "dawn", "dusk", "breeze", "gale",
            "hurricane", "tornado", "blizzard", "avalanche", "drought", "flood", "mist", "fog", "frost", "dew",

            // Arts & Culture (50 words)
            "painting", "sculpture", "drawing", "sketch", "portrait", "landscape", "abstract", "realistic", "impressionist", "modern",
            "canvas", "brush", "palette", "easel", "gallery", "museum", "exhibition", "masterpiece", "artist", "creativity",
            "music", "symphony", "orchestra", "conductor", "composer", "melody", "harmony", "rhythm", "tempo", "instrument",
            "piano", "violin", "guitar", "drums", "flute", "saxophone", "trumpet", "cello", "harp", "opera",
            "theater", "drama", "comedy", "tragedy", "actor", "actress", "director", "producer", "script", "stage",

            // Transportation (50 words)
            "automobile", "motorcycle", "bicycle", "scooter", "skateboard", "helicopter", "airplane", "jet", "rocket", "spaceship",
            "train", "subway", "tram", "bus", "truck", "van", "taxi", "limousine", "boat", "yacht",
            "sailboat", "submarine", "ferry", "cruise", "highway", "street", "avenue", "boulevard", "intersection", "bridge",
            "tunnel", "station", "airport", "harbor", "port", "garage", "parking", "traffic", "passenger", "driver",
            "pilot", "captain", "navigator", "mechanic", "engineer", "conductor", "journey", "destination", "route", "schedule",

            // Emotions & Feelings (50 words)
            "happiness", "joy", "excitement", "enthusiasm", "delight", "pleasure", "satisfaction", "contentment", "peace", "serenity",
            "sadness", "melancholy", "grief", "sorrow", "disappointment", "frustration", "anger", "rage", "fury", "irritation",
            "fear", "anxiety", "worry", "nervousness", "panic", "terror", "courage", "bravery", "confidence", "determination",
            "love", "affection", "tenderness", "compassion", "empathy", "kindness", "generosity", "gratitude", "appreciation", "admiration",
            "surprise", "amazement", "wonder", "curiosity", "interest", "fascination", "inspiration", "motivation", "hope", "optimism"
        };
        return words;
    }

    private string[] bahav2()
    {
        string[] words = new string[]
        {
            // Objects (50 words)
            "table", "chair", "sofa", "bed", "wardrobe", "desk", "lamp", "bookshelf", "television", "refrigerator",
            "stove", "oven", "microwave", "dishwasher", "washing machine", "dryer", "vacuum cleaner", "broom", "mop", "bucket",
            "sink", "toilet", "bathtub", "shower", "mirror", "window", "door", "wall", "floor", "ceiling",
            "roof", "garden", "yard", "fence", "gate", "driveway", "garage", "car", "bicycle", "motorcycle",
            "bus", "train", "airplane", "boat", "ship", "bridge", "road", "path", "street", "avenue",

            // Actions (50 words)
            "run", "walk", "jump", "swim", "dance", "sing", "read", "write", "draw", "paint",
            "cook", "bake", "clean", "wash", "drive", "fly", "sail", "ride", "climb", "fall",
            "laugh", "cry", "smile", "frown", "sleep", "wake", "eat", "drink", "talk", "listen",
            "watch", "see", "hear", "feel", "touch", "smell", "taste", "think", "believe", "know",
            "learn", "teach", "study", "work", "play", "rest", "travel", "explore", "discover", "create",

            // Descriptions (50 words)
            "big", "small", "large", "tiny", "huge", "little", "tall", "short", "long", "wide",
            "narrow", "thick", "thin", "heavy", "light", "dark", "bright", "colorful", "plain", "simple",
            "complex", "easy", "difficult", "hard", "soft", "loud", "quiet", "noisy", "clean", "dirty",
            "new", "old", "young", "ancient", "modern", "fast", "slow", "quick", "rapid", "gradual",
            "hot", "cold", "warm", "cool", "dry", "wet", "sharp", "dull", "smooth", "rough",

            // People (50 words)
            "man", "woman", "child", "baby", "boy", "girl", "teenager", "adult", "elderly", "person",
            "friend", "family", "parent", "mother", "father", "sister", "brother", "grandmother", "grandfather", "uncle",
            "aunt", "cousin", "nephew", "niece", "neighbor", "teacher", "student", "doctor", "nurse", "police officer",
            "firefighter", "soldier", "chef", "artist", "musician", "actor", "actress", "writer", "scientist", "engineer",
            "programmer", "designer", "manager", "worker", "boss", "customer", "client", "visitor", "guest", "stranger",

            // Animals (50 words)
            "dog", "cat", "bird", "fish", "horse", "cow", "pig", "sheep", "goat", "chicken",
            "duck", "goose", "rabbit", "mouse", "rat", "squirrel", "chipmunk", "beaver", "otter", "seal",
            "walrus", "elephant", "giraffe", "lion", "tiger", "bear", "wolf", "fox", "deer", "moose",
            "elk", "buffalo", "camel", "donkey", "mule", "zebra", "kangaroo", "koala", "panda", "monkey",
            "ape", "gorilla", "chimp", "orangutan", "whale", "dolphin", "shark", "octopus", "squid", "crab",

            // Nature (50 words)
            "tree", "flower", "grass", "bush", "shrub", "plant", "leaf", "branch", "root", "seed",
            "fruit", "vegetable", "nut", "berry", "mushroom", "stone", "rock", "mountain", "hill", "valley",
            "river", "stream", "lake", "pond", "ocean", "sea", "wave", "tide", "sand", "beach",
            "desert", "forest", "jungle", "swamp", "marsh", "cave", "cliff", "waterfall", "rain", "snow",
            "wind", "cloud", "fog", "mist", "sun", "moon", "star", "planet", "sky", "earth",

            // Time (50 words)
            "second", "minute", "hour", "day", "night", "week", "month", "year", "decade", "century",
            "morning", "afternoon", "evening", "dawn", "dusk", "today", "yesterday", "tomorrow", "now", "later",
            "soon", "early", "late", "past", "present", "future", "before", "after", "during", "while",
            "since", "until", "when", "whenever", "then", "next", "last", "first", "final", "permanent",
            "temporary", "quick", "slow", "fast", "gradual", "sudden", "immediate", "delayed", "prompt", "overdue",

            // Numbers & Quantity (50 words)
            "one", "two", "three", "four", "five", "six", "seven", "eight", "nine", "ten",
            "eleven", "twelve", "thirteen", "fourteen", "fifteen", "sixteen", "seventeen", "eighteen", "nineteen", "twenty",
            "thirty", "forty", "fifty", "sixty", "seventy", "eighty", "ninety", "hundred", "thousand", "million",
            "billion", "trillion", "zero", "half", "quarter", "double", "triple", "single", "pair", "dozen",
            "score", "gross", "percent", "fraction", "decimal", "integer", "whole", "part", "some", "all",

            // Directions & Positions (50 words)
            "up", "down", "left", "right", "forward", "backward", "north", "south", "east", "west",
            "above", "below", "beside", "between", "among", "inside", "outside", "under", "over", "through",
            "across", "around", "behind", "in front of", "next to", "near", "far", "close", "distant", "adjacent",
            "opposite", "parallel", "perpendicular", "horizontal", "vertical", "straight", "curved", "bent", "crooked", "zigzag",
            "spiral", "circular", "square", "triangular", "rectangular", "round", "flat", "pointed", "sharp", "blunt"
        };
        return words;
    }

    private void btnDeneme14_Click(object sender, EventArgs e)
    {
        string[] words = baha4();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < words.Length; i++)
        {
            sb.Append(words[i]);
            if (i < words.Length - 1)
                sb.Append(" ");
        }
        string message = sb.ToString();
        int tokenCount = message.Length / 4;
        int charCount = message.Length;
        int wordCount = words.Length;
        MessageBox.Show($"Number of tokens: {tokenCount}\nNumber of words: {wordCount}\nNumber of characters: {charCount}\n\n{message}");
    }
    private string[] baha9()
    {
        List<string> words = new List<string>();
        for (int i = 1; i <= 400; i++)
        {
            words.Add("baha9_word" + i);
        }
        return words.ToArray();
    }

    private string[] burki1()
    {
        string[] flowers = new string[]
        {
            "Rose", "Tulip", "Daisy", "Sunflower", "Lily", "Orchid", "Carnation", "Daffodil", "Iris", "Peony",
            "Jasmine", "Lavender", "Marigold", "Poppy", "Violet", "Zinnia", "Aster", "Begonia", "Camellia", "Dahlia",
            "Freesia", "Gardenia", "Hibiscus", "Impatiens", "Jonquil", "Kalanchoe", "Lilac", "Magnolia", "Narcissus", "Oleander",
            "Pansy", "Quince", "Ranunculus", "Snapdragon", "Tuberose", "Umbrella", "Verbena", "Wisteria", "Xeranthemum", "Yarrow",
            "Azalea", "Bluebell", "Chrysanthemum", "Delphinium", "Edelweiss", "Forget-me-not", "Geranium", "Hyacinth", "Ivy", "Jade",
            "Kalmia", "Larkspur", "Morning Glory", "Nasturtium", "Oxeye", "Primrose", "Queen Anne's Lace", "Rhododendron", "Salvia", "Thistle",
            "Ulex", "Vinca", "Wallflower", "Xylem", "Yellow Bell", "Zephyr", "Anemone", "Buttercup", "Columbine", "Dusty Miller",
            "Evening Primrose", "Foxglove", "Gladiolus", "Hollyhock", "Indian Paintbrush", "Japanese Maple", "Kiwi", "Lotus", "Moonflower", "Nigella",
            "Orange Blossom", "Petunia", "Queen's Crown", "Red Hot Poker", "Sweet Pea", "Tiger Lily", "Upright Spurge", "Viper's Bugloss", "Wild Rose", "Xanthium",
            "Yellow Jasmine", "Zebra Plant", "Alpine Forget-me-not", "Baby's Breath", "Coral Bells", "Datura", "English Daisy", "Flame Flower", "Globe Thistle", "Hosta"
        };
        return flowers;
    }

    private string[] burki2()
    {
        string[] flowers = new string[]
        {
            "Acacia", "Bird of Paradise", "Calla Lily", "Delphiniums", "Eucalyptus", "Fuchsia", "Gerbera", "Heather", "Iceland Poppy", "Japanese Anemone",
            "King Protea", "Lisianthus", "Mimosa", "Nemesia", "Oxalis", "Protea", "Queen of the Night", "Rudbeckia", "Statice", "Tithonia",
            "Urtica", "Veronica", "Water Lily", "Xanthoceras", "Yellow Cosmos", "Zonal Geranium", "Alstroemeria", "Black-eyed Susan", "Celosia", "Dianthus",
            "Echinacea", "Flame Nettle", "Gaillardia", "Heliotrope", "Ipomoea", "Japanese Camellia", "Kniphofia", "Lavatera", "Monarda", "Nicotiana",
            "Osteospermum", "Phlox", "Quamoclit", "Rudbeckia", "Scabiosa", "Torenia", "Ursinia", "Vanda", "Wax Flower", "Xerophyllum",
            "Yellow Flag", "Zantedeschia", "Anthurium", "Browallia", "Catharanthus", "Digitalis", "Eryngium", "Foeniculum", "Gomphrena", "Helenium",
            "Ilex", "Japanese Pieris", "Kochia", "Leucanthemum", "Mirabilis", "Nierembergia", "Oenothera", "Portulaca", "Quercus", "Ricinus",
            "Sedum", "Tagetes", "Uvularia", "Viburnum", "Weigela", "Xanthorrhiza", "Yellow Archangel", "Zephyranthes", "Achillea", "Buddleia",
            "Coreopsis", "Doronicum", "Erysimum", "Filipendula", "Gypsophila", "Heuchera", "Incarvillea", "Japanese Spurge", "Knautia", "Lamium",
            "Melissa", "Nepeta", "Origanum", "Polemonium", "Quassia", "Rosmarinus", "Santolina", "Teucrium", "Urginea", "Valeriana"
        };
        return flowers;
    }

    private string[] burki3()
    {
        string[] flowers = new string[]
        {
            "Amaryllis", "Bougainvillea", "Cyclamen", "Dusty Miller", "Eustoma", "Flame Lily", "Gladiola", "Hellebore", "Ixia", "Japanese Iris",
            "Kangaroo Paw", "Liatris", "Muscari", "Nerine", "Ornithogalum", "Penstemon", "Quill Chrysanthemum", "Ranunculus", "Solidago", "Tritoma",
            "Umbrella Plant", "Veronicas", "Windflower", "Xylosma", "Yellow Bells", "Zygocactus", "Agapanthus", "Balloon Flower", "Coleus", "Dicentra",
            "Eremurus", "Fritillaria", "Gaura", "Helianthus", "Ipheion", "Japanese Maple", "Kerria", "Leucojum", "Monkshood", "Nandina",
            "Osmanthus", "Pachysandra", "Quince Blossom", "Rodgersia", "Spiraea", "Trollius", "Uva Ursi", "Violas", "Wild Ginger", "Xyris",
            "Yellow Wood", "Zamioculcas", "Aconitum", "Brunnera", "Campanula", "Dianella", "Epimedium", "Ferns", "Galanthus", "Hepatica",
            "Iberis", "Japanese Painted Fern", "Kirengeshoma", "Ligularia", "Mertensia", "Ophiopogon", "Pulmonaria", "Rodgersia", "Saxifraga", "Tiarella",
            "Uvularia", "Vancouveria", "Wild Columbine", "Xanthorhiza", "Yellow Trout Lily", "Zigadenus", "Ajuga", "Bergenia", "Corydalis", "Disporum",
            "Erythronium", "Galium", "Hakonechloa", "Hosta", "Jeffersonia", "Kirengeshoma", "Lamiastrum", "Maianthemum", "Omphalodes", "Pachyphragma",
            "Ranunculus", "Sanguinaria", "Trillium", "Uvularia", "Vinca", "Waldsteinia", "Xanthorhiza", "Yellow Corydalis", "Zizia", "Asarum"
        };
        return flowers;
    }

    private string[] burki4()
    {
        string[] flowers = new string[]
        {
            "Abutilon", "Brachyscome", "Catmint", "Diascia", "Echium", "Felicia", "Gazania", "Heliophila", "Iochroma", "Jacobinia",
            "Kalimeris", "Lobularia", "Mimulus", "Nemophila", "Osteospermum", "Plectranthus", "Quamoclit", "Rhodanthe", "Sutera", "Thunbergia",
            "Ursinia", "Verbascum", "Wahlenbergia", "Xeranthemum", "Yellow Alyssum", "Zingiber", "Angelonia", "Bacopa", "Cuphea", "Dichondra",
            "Evolvulus", "Fuchsia", "Glechoma", "Hedera", "Iresine", "Juncus", "Koelreuteria", "Lysimachia", "Mecardonia", "Nolana",
            "Oxypetalum", "Petunia", "Quercus", "Ruellia", "Scaevola", "Tradescantia", "Utricularia", "Vinca", "Wedelia", "Xylosma",
            "Yellow Sage", "Zoysia", "Alternanthera", "Bidens", "Coleonema", "Duranta", "Erigeron", "Ficus", "Grevillea", "Hebe",
            "Ilex", "Juniperus", "Kalanchoe", "Lantana", "Myoporum", "Nandina", "Olearia", "Pittosporum", "Quercus", "Rosmarinus",
            "Salvia", "Teucrium", "Ulmus", "Viburnum", "Westringia", "Xylosma", "Yucca", "Ziziphus", "Abelia", "Berberis",
            "Cotoneaster", "Daphne", "Escallonia", "Forsythia", "Gardenia", "Hamamelis", "Ilex", "Jasminum", "Kerria", "Ligustrum",
            "Mahonia", "Nandina", "Osmanthus", "Pieris", "Quercus", "Rhododendron", "Skimmia", "Taxus", "Ulmus", "Viburnum"
        };
        return flowers;
    }

    private string[] burki5()
    {
        string[] flowers = new string[]
        {
            "Aconitum", "Buddleja", "Clematis", "Delphinium", "Echinacea", "Foxglove", "Geum", "Hemerocallis", "Inula", "Japanese Anemone",
            "Kniphofia", "Lupinus", "Monarda", "Nepeta", "Oenothera", "Paeonia", "Quercus", "Rudbeckia", "Sedum", "Thalictrum",
            "Urtica", "Verbena", "Wisteria", "Xeranthemum", "Yucca", "Zantedeschia", "Astilbe", "Brunnera", "Campanula", "Dicentra",
            "Epimedium", "Filipendula", "Geranium", "Heuchera", "Iris", "Japanese Painted Fern", "Kirengeshoma", "Ligularia", "Mertensia", "Ophiopogon",
            "Pulmonaria", "Rodgersia", "Saxifraga", "Tiarella", "Uvularia", "Vancouveria", "Wild Columbine", "Xanthorhiza", "Yellow Trout Lily", "Zigadenus",
            "Ajuga", "Bergenia", "Corydalis", "Disporum", "Erythronium", "Galium", "Hakonechloa", "Hosta", "Jeffersonia", "Kirengeshoma",
            "Lamiastrum", "Maianthemum", "Omphalodes", "Pachyphragma", "Ranunculus", "Sanguinaria", "Trillium", "Uvularia", "Vinca", "Waldsteinia",
            "Xanthorhiza", "Yellow Corydalis", "Zizia", "Asarum", "Bloodroot", "Cimicifuga", "Darmera", "Epimedium", "Ferns", "Galanthus",
            "Hepatica", "Iberis", "Japanese Painted Fern", "Kirengeshoma", "Ligularia", "Mertensia", "Ophiopogon", "Pulmonaria", "Rodgersia", "Saxifraga",
            "Tiarella", "Uvularia", "Vancouveria", "Wild Ginger", "Xyris", "Yellow Wood", "Zamioculcas", "Aconitum", "Brunnera", "Campanula"
        };
        return flowers;
    }

    private string[] burki6()
    {
        string[] flowers = new string[]
        {
            "Amaranthus", "Balsam", "Celosia", "Dusty Miller", "Euphorbia", "Four O'Clock", "Globe Amaranth", "Helichrysum", "Impatiens", "Joseph's Coat",
            "Kochia", "Love-in-a-Mist", "Marigold", "Nicotiana", "Ornamental Cabbage", "Portulaca", "Queen Anne's Lace", "Ricinus", "Salvia", "Torenia",
            "Urtica", "Vinca", "Wax Begonia", "Xeranthemum", "Yellow Cosmos", "Zinnia", "Ageratum", "Browallia", "Catharanthus", "Dianthus",
            "Echinacea", "Fuchsia", "Gomphrena", "Heliotrope", "Ipomoea", "Japanese Camellia", "Kalanchoe", "Lobelia", "Mimosa", "Nemesia",
            "Oxalis", "Petunia", "Quamoclit", "Rudbeckia", "Scabiosa", "Tagetes", "Ursinia", "Verbena", "Wallflower", "Xylem",
            "Yellow Flag", "Zantedeschia", "Antirrhinum", "Begonia", "Coleus", "Dahlia", "Eustoma", "Fuchsia", "Gazania", "Helianthus",
            "Impatiens", "Jacobinia", "Kalimeris", "Lobularia", "Mimulus", "Nemophila", "Osteospermum", "Plectranthus", "Quamoclit", "Rhodanthe",
            "Sutera", "Thunbergia", "Ursinia", "Verbascum", "Wahlenbergia", "Xeranthemum", "Yellow Alyssum", "Zingiber", "Angelonia", "Bacopa",
            "Cuphea", "Dichondra", "Evolvulus", "Fuchsia", "Glechoma", "Hedera", "Iresine", "Juncus", "Koelreuteria", "Lysimachia",
            "Mecardonia", "Nolana", "Oxypetalum", "Petunia", "Quercus", "Ruellia", "Scaevola", "Tradescantia", "Utricularia", "Vinca"
        };
        return flowers;
    }

    private string[] burki7()
    {
        string[] flowers = new string[]
        {
            "Acanthus", "Balloon Flower", "Catmint", "Delphiniums", "Echinacea", "False Indigo", "Goat's Beard", "Hardy Hibiscus", "Iris", "Japanese Anemone",
            "Korean Spice", "Lamb's Ear", "Meadowsweet", "New England Aster", "Obedient Plant", "Peony", "Queen of the Prairie", "Russian Sage", "Shasta Daisy", "Tickseed",
            "Upright Sedum", "Veronica", "Wild Bergamot", "Yarrow", "Yellow Coneflower", "Zebra Grass", "Astilbe", "Bee Balm", "Cardinal Flower", "Daylily",
            "Evening Primrose", "Foxglove", "Garden Phlox", "Hollyhock", "Ironweed", "Joe Pye Weed", "Kansas Gayfeather", "Liatris", "Monkshood", "Northern Sea Oats",
            "Orange Coneflower", "Purple Coneflower", "Queen Anne's Lace", "Red Hot Poker", "Sneezeweed", "Turtlehead", "Upright Spurge", "Violet", "Wild Ginger", "Xyris",
            "Yellow Loosestrife", "Zigzag Goldenrod", "Autumn Joy", "Black-eyed Susan", "Coral Bells", "Dead Nettle", "Elephant Ear", "Foamflower", "Goatsbeard", "Heuchera",
            "Indian Pink", "Japanese Spurge", "Knotweed", "Lungwort", "Mayapple", "Northern Maidenhair", "Ostrich Fern", "Pachysandra", "Queen of the Meadow", "Royal Fern",
            "Solomon's Seal", "Trout Lily", "Umbrella Leaf", "Virginia Bluebells", "Wild Columbine", "Xylem", "Yellow Trout Lily", "Zigadenus", "Alumroot", "Bunchberry",
            "Christmas Fern", "Dutchman's Breeches", "Early Meadow Rue", "False Solomon's Seal", "Goldenseal", "Hepatica", "Indian Turnip", "Jack-in-the-Pulpit", "Kentucky Ginger", "Lady Fern",
            "Maidenhair Fern", "New York Fern", "Oak Fern", "Partridgeberry", "Quaking Aspen", "Rattlesnake Fern", "Sensitive Fern", "Trillium", "Umbrella Tree", "Violet Wood Sorrel"
        };
        return flowers;
    }

    private string[] burki8()
    {
        string[] flowers = new string[]
        {
            "African Daisy", "Bachelor Button", "Candytuft", "Dame's Rocket", "English Daisy", "Feverfew", "Godetia", "Honesty", "Iceland Poppy", "Johnny Jump Up",
            "Kiss-me-over-the-garden-gate", "Larkspur", "Money Plant", "Night-scented Stock", "Opium Poppy", "Pot Marigold", "Quaking Grass", "Rocket", "Sweet Alyssum", "Toadflax",
            "Unicorn Plant", "Venus Looking Glass", "Wild Carrot", "Xeranthemum", "Yellow Rattle", "Zinnia", "Annual Phlox", "Baby Blue Eyes", "Calendula", "Dill",
            "Eschscholzia", "Flax", "Gypsophila", "Helianthus", "Iberis", "Japanese Honeysuckle", "Kochia", "Love-lies-bleeding", "Mignonette", "Nasturtium",
            "Ornamental Grass", "Painted Tongue", "Quail Grass", "Red Orach", "Swan River Daisy", "Tidy Tips", "Ursinia", "Viscaria", "Wheat", "Xylem",
            "Yellow Horned Poppy", "Zea", "Ammi", "Bells of Ireland", "Clarkia", "Datura", "Emilia", "Felicia", "Gilia", "Hunnemannia",
            "Ipomoea", "Job's Tears", "Kaempferia", "Linum", "Moluccella", "Nigella", "Omphalodes", "Papaver", "Quercus", "Reseda",
            "Silene", "Tropaeolum", "Urtica", "Venidium", "Whitlavia", "Xerophyllum", "Yellow Cosmos", "Zephyranthes", "Anagallis", "Brachycome",
            "Centaurea", "Dimorphotheca", "Echium", "Foeniculum", "Gomphrena", "Heliophila", "Iochroma", "Jacobinia", "Kalimeris", "Lobularia",
            "Mimulus", "Nemophila", "Osteospermum", "Plectranthus", "Quamoclit", "Rhodanthe", "Sutera", "Thunbergia", "Ursinia", "Verbascum"
        };
        return flowers;
    }

    private string[] burki9()
    {
        string[] flowers = new string[]
        {
            "Alpine Aster", "Blazing Star", "Columbine", "Desert Marigold", "Evening Star", "Fireweed", "Ghost Plant", "Harebell", "Indian Blanket", "Jewelweed",
            "King's Crown", "Lupine", "Mountain Laurel", "Northern Bedstraw", "Old Man's Beard", "Pasque Flower", "Queen's Cup", "Red Clover", "Shooting Star", "Trapper's Tea",
            "Utah Serviceberry", "Violet", "Wild Rose", "Xylem", "Yellow Monkey Flower", "Zigzag Clover", "Alpine Forget-me-not", "Bitterroot", "Cow Parsnip", "Dogbane",
            "Elephant Head", "Fireweed", "Glacier Lily", "Heartleaf Arnica", "Indian Paintbrush", "Jacob's Ladder", "Kinnikinnick", "Lewis Flax", "Mountain Ash", "Nodding Onion",
            "Oregon Grape", "Prickly Pear", "Queen Anne's Lace", "Rocky Mountain Iris", "Sticky Geranium", "Twin Berry", "Utah Honeysuckle", "Violet Green Swallow", "Wild Strawberry", "Xyris",
            "Yellow Bell", "Zigzag Goldenrod", "Arrowleaf Balsamroot", "Beargrass", "Camas", "Death Camas", "Elderberry", "False Hellebore", "Glacier Lily", "Huckleberry",
            "Indian Pipe", "June Grass", "Kinnikinnick", "Labrador Tea", "Mountain Goat", "Northern Gentian", "Oregon Grape", "Pearly Everlasting", "Queen's Cup", "Red Elderberry",
            "Serviceberry", "Thimbleberry", "Utah Juniper", "Vanilla Leaf", "Wild Ginger", "Xylem", "Yellow Avalanche Lily", "Zigzag Clover", "Alpine Buttercup", "Bunchberry",
            "Cloudberry", "Devil's Club", "Enchanter's Nightshade", "Foam Flower", "Goatsbeard", "Heartleaf Arnica", "Inside-out Flower", "Juniper", "Kinnikinnick", "Labrador Tea",
            "Mountain Ash", "Northern Bedstraw", "Oregon Grape", "Pipsissewa", "Queen's Cup", "Red Huckleberry", "Salmonberry", "Twinflower", "Utah Serviceberry", "Vanilla Leaf"
        };
        return flowers;
    }

    private string[] burki10()
    {
        string[] flowers = new string[]
        {
            "Acacia", "Bottlebrush", "Callistemon", "Desert Rose", "Eucalyptus", "Flame Tree", "Grevillea", "Hakea", "Ironbark", "Jacaranda",
            "Kangaroo Paw", "Lemon Myrtle", "Melaleuca", "Native Fuchsia", "Olearia", "Protea", "Quandong", "Red Gum", "Sturt's Desert Pea", "Tea Tree",
            "Umbrella Tree", "Violet Honey Myrtle", "Wattle", "Xanthorrhoea", "Yellow Bottlebrush", "Zieria", "Australian Fuchsia", "Banksia", "Calothamnus", "Dryandra",
            "Eremophila", "Ficus", "Gastrolobium", "Hardenbergia", "Isopogon", "Jacksonia", "Kennedia", "Lambertia", "Macadamia", "Nuytsia",
            "Oxylobium", "Pimelea", "Quercus", "Regelia", "Sollya", "Templetonia", "Ulex", "Verticordia", "Westringia", "Xylomelum",
            "Yellow Pea", "Zygophyllum", "Adenanthos", "Beaufortia", "Conospermum", "Darwinia", "Eremaea", "Frankenia", "Goodenia", "Hibbertia",
            "Isotropis", "Johnsonia", "Kunzea", "Lechenaultia", "Melaleuca", "Nemcia", "Orthrosanthus", "Petrophile", "Qualup Bell", "Ricinocarpos",
            "Scaevola", "Thryptomene", "Ursinia", "Verticordia", "Wahlenbergia", "Xanthosia", "Yellow Bottlebrush", "Zieria", "Actinotus", "Brachyscome",
            "Calandrinia", "Dampiera", "Epacris", "Flannel Flower", "Goodia", "Hovea", "Isotoma", "Jumping Jack Wattle", "Kunzea", "Leschenaultia",
            "Melaluca", "Native Violet", "Orthrosanthus", "Pink Rock Orchid", "Quandong", "Rock Isotome", "Stipa", "Trigger Plant", "Ursinia", "Violet Honey Myrtle"
        };
        return flowers;
    }

    private string[] ebru1()
    {
        string[] flowers = new string[]
        {
            "Abutilon", "Acacia", "Achillea", "Aconitum", "Adenium", "Agapanthus", "Ageratum", "Ajuga", "Alcea", "Alchemilla",
            "Allium", "Alstroemeria", "Alyssum", "Amaranthus", "Amaryllis", "Anemone", "Angelonia", "Anthurium", "Antirrhinum", "Aquilegia",
            "Arabis", "Arctotis", "Armeria", "Artemisia", "Asclepias", "Aster", "Astilbe", "Aubrieta", "Azalea", "Bacopa",
            "Baptisia", "Begonia", "Bellis", "Bergenia", "Bougainvillea", "Brachyscome", "Browallia", "Buddleja", "Caladium", "Calendula",
            "Calibrachoa", "Calla", "Callistephus", "Camellia", "Campanula", "Canna", "Capsicum", "Catharanthus", "Celosia", "Centaurea",
            "Centranthus", "Cerastium", "Cheiranthus", "Chrysanthemum", "Cineraria", "Clarkia", "Cleome", "Coleus", "Convolvulus", "Coreopsis",
            "Cosmos", "Crocosmia", "Crocus", "Cyclamen", "Dahlia", "Delphinium", "Dianthus", "Diascia", "Digitalis", "Dimorphotheca",
            "Doronicum", "Echinacea", "Echinops", "Echium", "Eryngium", "Erysimum", "Eschscholzia", "Euphorbia", "Eustoma", "Felicia",
            "Fuchsia", "Gaillardia", "Galanthus", "Gazania", "Gentiana", "Geranium", "Gerbera", "Geum", "Gladiolus", "Gomphrena",
            "Gypsophila", "Hedera", "Helenium", "Helianthus", "Helichrysum", "Heliotropium", "Helleborus", "Hemerocallis", "Heuchera", "Hibiscus"
        };
        return flowers;
    }

    private string[] ebru2()
    {
        string[] flowers = new string[]
        {
            "Hosta", "Hyacinthus", "Hydrangea", "Iberis", "Impatiens", "Ipomoea", "Iris", "Ixia", "Jasminum", "Kalanchoe",
            "Kniphofia", "Lamium", "Lantana", "Lathyrus", "Lavandula", "Lavatera", "Leucanthemum", "Liatris", "Lilium", "Limonium",
            "Linaria", "Linum", "Lobelia", "Lobularia", "Lunaria", "Lupinus", "Lychnis", "Lysimachia", "Malva", "Marigold",
            "Matthiola", "Meconopsis", "Mimulus", "Mirabilis", "Monarda", "Muscari", "Myosotis", "Narcissus", "Nasturtium", "Nemesia",
            "Nemophila", "Nepeta", "Nicotiana", "Nigella", "Oenothera", "Osteospermum", "Paeonia", "Pansy", "Papaver", "Pelargonium",
            "Penstemon", "Petunia", "Phlox", "Physalis", "Platycodon", "Plumbago", "Polemonium", "Polyanthus", "Portulaca", "Primula",
            "Pulmonaria", "Ranunculus", "Reseda", "Rhodanthe", "Ricinus", "Rudbeckia", "Salvia", "Sanvitalia", "Scabiosa", "Schizanthus",
            "Sedum", "Senecio", "Silene", "Solidago", "Stachys", "Statice", "Stocks", "Sunflower", "Sweet Pea", "Tagetes",
            "Tanacetum", "Thalictrum", "Thunbergia", "Thymus", "Tithonia", "Torenia", "Tradescantia", "Trollius", "Tropaeolum", "Tulipa",
            "Verbascum", "Verbena", "Veronica", "Vinca", "Viola", "Wallflower", "Xeranthemum", "Zinnia", "Zonal Geranium", "Zygopetalum"
        };
        return flowers;
    }

    private string[] ebru3()
    {
        string[] flowers = new string[]
        {
            "African Violet", "Alpine Pink", "Angel's Trumpet", "Baby's Breath", "Bachelor's Button", "Balloon Flower", "Bee Balm", "Bell Flower", "Bird of Paradise", "Black-eyed Susan",
            "Blanket Flower", "Bleeding Heart", "Blue Star", "Butterfly Bush", "Butterfly Weed", "Candytuft", "Cardinal Flower", "Carnation", "Cathedral Bells", "Chinese Lantern",
            "Christmas Rose", "Cockscomb", "Columbine", "Coral Bells", "Cornflower", "Crown Imperial", "Daffodil", "Dame's Rocket", "Dead Nettle", "Desert Rose",
            "Dusty Miller", "Dutch Iris", "Evening Primrose", "False Spirea", "Farewell to Spring", "Feverfew", "Fire Pink", "Flame Flower", "Floss Flower", "Forget-me-not",
            "Four O'Clock", "Foxglove", "French Marigold", "Garden Balsam", "Garden Phlox", "Gas Plant", "Globe Amaranth", "Globe Thistle", "Glory of the Snow", "Godetia",
            "Golden Marguerite", "Grape Hyacinth", "Hardy Geranium", "Hens and Chicks", "Hollyhock", "Honesty", "Hosta", "Ice Plant", "Indian Blanket", "Japanese Anemone",
            "Japanese Painted Fern", "Johnny Jump Up", "Joseph's Coat", "King's Spear", "Lady's Mantle", "Lamb's Ear", "Larkspur", "Lemon Balm", "Lily of the Valley", "Love-in-a-Mist",
            "Love-lies-bleeding", "Madagascar Periwinkle", "Maltese Cross", "Meadow Rue", "Mexican Sunflower", "Mignonette", "Million Bells", "Monkey Flower", "Moonflower", "Morning Glory",
            "Moss Rose", "Mountain Laurel", "Nasturtium", "New Guinea Impatiens", "Night-scented Stock", "Obedient Plant", "Old Man's Beard", "Ornamental Cabbage", "Painted Daisy", "Painted Tongue",
            "Pasque Flower", "Patience Plant", "Periwinkle", "Persian Shield", "Pincushion Flower", "Pink Evening Primrose", "Pot Marigold", "Prairie Gentian", "Prickly Poppy", "Prince's Feather"
        };
        return flowers;
    }

    private string[] ebru4()
    {
        string[] flowers = new string[]
        {
            "Queen Anne's Lace", "Red Hot Poker", "Rock Cress", "Rose Campion", "Rose of Sharon", "Russian Sage", "Sea Holly", "Sea Lavender", "Shasta Daisy", "Shooting Star",
            "Siberian Iris", "Silver Dust", "Snapdragon", "Snow-in-Summer", "Snow-on-the-Mountain", "Spider Flower", "Spotted Dead Nettle", "Star of Bethlehem", "Statice", "Stock",
            "Stonecrop", "Summer Cypress", "Swan River Daisy", "Sweet Alyssum", "Sweet Pea", "Sweet William", "Sword Lily", "Tidy Tips", "Tiger Lily", "Toad Lily",
            "Torch Lily", "Tree Mallow", "Tuberose", "Turtlehead", "Umbrella Plant", "Valerian", "Velvet Flower", "Venus Looking Glass", "Viper's Bugloss", "Virginia Bluebells",
            "Wake Robin", "Wax Begonia", "Wild Bergamot", "Wild Ginger", "Windflower", "Winter Aconite", "Wishbone Flower", "Wood Hyacinth", "Woolly Thyme", "Yarrow",
            "Yellow Archangel", "Yellow Bells", "Yellow Cosmos", "Yellow Flag", "Yellow Loosestrife", "Youth and Beauty", "Zebra Plant", "Zephyr Lily", "Zigzag Goldenrod", "Zinnia",
            "Aethionema", "Agrostemma", "Alonsoa", "Ammobium", "Anagallis", "Anchusa", "Anemone", "Angelica", "Anoda", "Antirrhinum",
            "Arctotis", "Argemone", "Asarina", "Asperula", "Atriplex", "Bartonia", "Bassia", "Bidens", "Brachycome", "Brassica",
            "Briza", "Bromus", "Calandrinia", "Calceolaria", "Callirhoe", "Callistephus", "Campanula", "Capsella", "Cardiospermum", "Carthamus",
            "Catananche", "Cerinthe", "Chaenostoma", "Chamaemelum", "Cheiranthus", "Chenopodium", "Chrysanthemum", "Cirsium", "Clarkia", "Cleome"
        };
        return flowers;
    }

    private string[] ebru5()
    {
        string[] flowers = new string[]
        {
            "Cobaea", "Collinsia", "Consolida", "Convolvulus", "Coreopsis", "Corydalis", "Cosmos", "Craspedia", "Crepis", "Cuphea",
            "Cynoglossum", "Dahlia", "Datura", "Daucus", "Delphinium", "Dianthus", "Diascia", "Didiscus", "Digitalis", "Dimorphotheca",
            "Dipsacus", "Dorotheanthus", "Dracocephalum", "Dyssodia", "Eccremocarpus", "Echium", "Emilia", "Eragrostis", "Eryngium", "Erysimum",
            "Eschscholzia", "Eucalyptus", "Euphorbia", "Eutoca", "Exacum", "Felicia", "Foeniculum", "Gaillardia", "Gazania", "Gilia",
            "Glaucium", "Gomphrena", "Grasses", "Gypsophila", "Helianthus", "Helichrysum", "Heliophila", "Heliotropium", "Hesperis", "Hibiscus",
            "Hordeum", "Hunnemannia", "Iberis", "Impatiens", "Ipomoea", "Isatis", "Kochia", "Lagurus", "Lathyrus", "Lavatera",
            "Layia", "Lepidium", "Leptosiphon", "Limnanthes", "Limonium", "Linaria", "Linum", "Lobelia", "Lobularia", "Lonas",
            "Lunaria", "Lupinus", "Lychnis", "Malcolmia", "Malope", "Malva", "Matricaria", "Matthiola", "Meconopsis", "Mentzelia",
            "Mesembryanthemum", "Mimulus", "Mirabilis", "Moluccella", "Myosotis", "Nemesia", "Nemophila", "Nicandra", "Nicotiana", "Nierembergia",
            "Nigella", "Nolana", "Oenothera", "Omphalodes", "Onopordum", "Orlaya", "Osteospermum", "Oxypetalum", "Papaver", "Pelargonium"
        };
        return flowers;
    }

    private string[] ebru6()
    {
        string[] flowers = new string[]
        {
            "Pennisetum", "Perilla", "Petunia", "Phacelia", "Phlox", "Physalis", "Platystemon", "Plectranthus", "Polemonium", "Polygonum",
            "Portulaca", "Proboscidea", "Psylliostachys", "Ptilotus", "Quamoclit", "Reseda", "Rhodanthe", "Ricinus", "Rudbeckia", "Salpiglossis",
            "Salvia", "Sanvitalia", "Saponaria", "Scabiosa", "Schizanthus", "Schizophragma", "Sedum", "Senecio", "Setaria", "Silene",
            "Silybum", "Solanum", "Sorghum", "Statice", "Sutera", "Tagetes", "Tanacetum", "Thunbergia", "Tithonia", "Torenia",
            "Trachelium", "Trachymene", "Trifolium", "Tropaeolum", "Tweedia", "Urtica", "Venidium", "Verbascum", "Verbena", "Veronica",
            "Vinca", "Viola", "Viscaria", "Wahlenbergia", "Xeranthemum", "Xerochrysum", "Zea", "Zinnia", "Zizania", "Ageratum",
            "Alcea", "Amaranthus", "Antirrhinum", "Aquilegia", "Aster", "Begonia", "Calendula", "Callistephus", "Campanula", "Celosia",
            "Centaurea", "Chrysanthemum", "Clarkia", "Cleome", "Coleus", "Consolida", "Convolvulus", "Coreopsis", "Cosmos", "Dahlia",
            "Delphinium", "Dianthus", "Digitalis", "Eschscholzia", "Gaillardia", "Gazania", "Gomphrena", "Gypsophila", "Helianthus", "Helichrysum",
            "Iberis", "Impatiens", "Ipomoea", "Lathyrus", "Lavatera", "Limonium", "Lobelia", "Lupinus", "Matthiola", "Mirabilis"
        };
        return flowers;
    }

    private string[] ebru7()
    {
        string[] flowers = new string[]
        {
            "Moluccella", "Myosotis", "Nasturtium", "Nemesia", "Nicotiana", "Nigella", "Papaver", "Petunia", "Phlox", "Portulaca",
            "Rudbeckia", "Salvia", "Scabiosa", "Tagetes", "Tropaeolum", "Verbena", "Viola", "Zinnia", "Abronia", "Acanthus",
            "Achillea", "Aconitum", "Actaea", "Adenophora", "Adonis", "Aegopodium", "Aethionema", "Agastache", "Agave", "Ageratum",
            "Agrostemma", "Ajuga", "Alcea", "Alchemilla", "Allium", "Alstroemeria", "Alyssum", "Amaranthus", "Amaryllis", "Ammi",
            "Ammobium", "Anemone", "Angelica", "Angelonia", "Anigozanthos", "Antennaria", "Anthemis", "Anthurium", "Antirrhinum", "Aquilegia",
            "Arabis", "Arctotis", "Arenaria", "Armeria", "Arnica", "Artemisia", "Arum", "Aruncus", "Asarum", "Asclepias",
            "Asparagus", "Asperula", "Aster", "Astilbe", "Astrantia", "Aubrieta", "Aurinia", "Azalea", "Bacopa", "Baptisia",
            "Begonia", "Bellis", "Bergenia", "Betonica", "Bidens", "Boltonia", "Bougainvillea", "Brachyscome", "Briza", "Browallia",
            "Brunnera", "Buddleja", "Buphtalmum", "Bupleurum", "Caladium", "Calamagrostis", "Calandrinia", "Calceolaria", "Calendula", "Calibrachoa",
            "Calla", "Callirhoe", "Callistemon", "Callistephus", "Calluna", "Caltha", "Camassia", "Camellia", "Campanula", "Canna"
        };
        return flowers;
    }

    private string[] ebru8()
    {
        string[] flowers = new string[]
        {
            "Capsicum", "Carex", "Catananche", "Catharanthus", "Catmint", "Celosia", "Centaurea", "Centranthus", "Cerastium", "Ceratostigma",
            "Chaenostoma", "Chamaecyparis", "Chelone", "Chionodoxa", "Chrysanthemum", "Cimicifuga", "Cineraria", "Clarkia", "Clematis", "Cleome",
            "Clivia", "Cobaea", "Colchicum", "Coleus", "Collinsia", "Colocasia", "Commelina", "Consolida", "Convallaria", "Convolvulus",
            "Coreopsis", "Cornus", "Cortaderia", "Corydalis", "Cosmos", "Cotinus", "Crambe", "Craspedia", "Crinum", "Crocosmia",
            "Crocus", "Cuphea", "Cyclamen", "Cynara", "Cynoglossum", "Dahlia", "Darmera", "Datura", "Daucus", "Delphinium",
            "Dendrobium", "Deschampsia", "Dianthus", "Diascia", "Dicentra", "Dictamnus", "Didiscus", "Digitalis", "Dimorphotheca", "Dipsacus",
            "Dodecatheon", "Doronicum", "Dracaena", "Dracocephalum", "Dryopteris", "Echinacea", "Echinops", "Echium", "Elymus", "Emilia",
            "Epilobium", "Epimedium", "Eragrostis", "Eremurus", "Erica", "Erigeron", "Eryngium", "Erysimum", "Eschscholzia", "Eucalyptus",
            "Eucomis", "Eupatorium", "Euphorbia", "Eustoma", "Exacum", "Fallopia", "Felicia", "Festuca", "Filipendula", "Foeniculum",
            "Fragaria", "Fritillaria", "Fuchsia", "Gaillardia", "Galanthus", "Galega", "Gaura", "Gazania", "Gentiana", "Geranium"
        };
        return flowers;
    }

    private string[] ebru9()
    {
        string[] flowers = new string[]
        {
            "Gerbera", "Geum", "Gilia", "Gladiolus", "Glaucium", "Globularia", "Gomphrena", "Grasses", "Gunnera", "Gypsophila",
            "Hakonechloa", "Hamamelis", "Hedera", "Hedychium", "Helenium", "Helianthemum", "Helianthus", "Helichrysum", "Heliophila", "Heliopsis",
            "Heliotropium", "Helleborus", "Hemerocallis", "Hepatica", "Hesperis", "Heuchera", "Hibiscus", "Hordeum", "Hosta", "Hunnemannia",
            "Hyacinthoides", "Hyacinthus", "Hydrangea", "Hyssopus", "Iberis", "Ilex", "Impatiens", "Imperata", "Incarvillea", "Inula",
            "Ipomoea", "Iris", "Isatis", "Ixia", "Jasione", "Jasminum", "Juncus", "Kalanchoe", "Kirengeshoma", "Knautia",
            "Kniphofia", "Kochia", "Lagurus", "Lamium", "Lantana", "Lathyrus", "Lavandula", "Lavatera", "Layia", "Leonotis",
            "Lepidium", "Leptosiphon", "Leucanthemum", "Leucojum", "Liatris", "Libertia", "Ligularia", "Lilium", "Limnanthes", "Limonium",
            "Linaria", "Linum", "Liriope", "Lobelia", "Lobularia", "Lonas", "Lunaria", "Lupinus", "Lychnis", "Lycoris",
            "Lysimachia", "Lythrum", "Macleaya", "Malcolmia", "Malope", "Malva", "Marrubium", "Matricaria", "Matthiola", "Meconopsis",
            "Melica", "Mentha", "Mentzelia", "Mertensia", "Mesembryanthemum", "Mimulus", "Mirabilis", "Miscanthus", "Molinia", "Moluccella"
        };
        return flowers;
    }

    private string[] ebru10()
    {
        string[] flowers = new string[]
        {
            "Monarda", "Muscari", "Myosotis", "Narcissus", "Nasturtium", "Nemesia", "Nemophila", "Nepeta", "Nerine", "Nicandra",
            "Nicotiana", "Nierembergia", "Nigella", "Nolana", "Oenothera", "Omphalodes", "Onopordum", "Ophiopogon", "Origanum", "Orlaya",
            "Ornithogalum", "Osteospermum", "Oxalis", "Oxypetalum", "Paeonia", "Panicum", "Pansy", "Papaver", "Passiflora", "Pelargonium",
            "Pennisetum", "Penstemon", "Perilla", "Persicaria", "Petunia", "Phacelia", "Phalaris", "Phleum", "Phlox", "Phormium",
            "Physalis", "Physostegia", "Phyteuma", "Platycodon", "Plectranthus", "Plumbago", "Polemonium", "Polygonatum", "Polygonum", "Polystichum",
            "Portulaca", "Potentilla", "Primula", "Proboscidea", "Prunella", "Psylliostachys", "Ptilotus", "Pulmonaria", "Pulsatilla", "Quamoclit",
            "Ranunculus", "Reseda", "Rhodanthe", "Rhodiola", "Ricinus", "Rodgersia", "Romneya", "Rudbeckia", "Ruta", "Sagina",
            "Salpiglossis", "Salvia", "Sanguinaria", "Sanguisorba", "Sanvitalia", "Saponaria", "Saxifraga", "Scabiosa", "Schizanthus", "Schizophragma",
            "Scilla", "Sedum", "Sempervivum", "Senecio", "Setaria", "Sidalcea", "Silene", "Silybum", "Sisyrinchium", "Smilacina",
            "Solanum", "Solidago", "Sorghum", "Stachys", "Statice", "Stipa", "Stokesia", "Sutera", "Symphytum", "Tagetes"
        };
        return flowers;
    }

    private string[] ebru11()
    {
        string[] flowers = new string[]
        {
            "Tanacetum", "Tellima", "Thalictrum", "Thermopsis", "Thunbergia", "Thymus", "Tiarella", "Tigridia", "Tithonia", "Torenia",
            "Trachelium", "Trachymene", "Tradescantia", "Trifolium", "Trillium", "Tritoma", "Trollius", "Tropaeolum", "Tulipa", "Tweedia",
            "Typha", "Urtica", "Uvularia", "Valeriana", "Venidium", "Verbascum", "Verbena", "Vernonia", "Veronica", "Viburnum",
            "Vinca", "Viola", "Viscaria", "Wahlenbergia", "Waldsteinia", "Weigela", "Xeranthemum", "Xerochrysum", "Yucca", "Zantedeschia",
            "Zea", "Zinnia", "Zizania", "Zygopetalum", "Abutilon", "Acacia", "Achillea", "Aconitum", "Adenium", "Agapanthus",
            "Ageratum", "Ajuga", "Alcea", "Alchemilla", "Allium", "Alstroemeria", "Alyssum", "Amaranthus", "Amaryllis", "Anemone",
            "Angelonia", "Anthurium", "Antirrhinum", "Aquilegia", "Arabis", "Arctotis", "Armeria", "Artemisia", "Asclepias", "Aster",
            "Astilbe", "Aubrieta", "Azalea", "Bacopa", "Baptisia", "Begonia", "Bellis", "Bergenia", "Bougainvillea", "Brachyscome",
            "Browallia", "Buddleja", "Caladium", "Calendula", "Calibrachoa", "Calla", "Callistephus", "Camellia", "Campanula", "Canna",
            "Capsicum", "Catharanthus", "Celosia", "Centaurea", "Centranthus", "Cerastium", "Cheiranthus", "Chrysanthemum", "Cineraria", "Clarkia"
        };
        return flowers;
    }

    private string[] ebru12()
    {
        string[] flowers = new string[]
        {
            "Cleome", "Coleus", "Convolvulus", "Coreopsis", "Cosmos", "Crocosmia", "Crocus", "Cyclamen", "Dahlia", "Delphinium",
            "Dianthus", "Diascia", "Digitalis", "Dimorphotheca", "Doronicum", "Echinacea", "Echinops", "Echium", "Eryngium", "Erysimum",
            "Eschscholzia", "Euphorbia", "Eustoma", "Felicia", "Fuchsia", "Gaillardia", "Galanthus", "Gazania", "Gentiana", "Geranium",
            "Gerbera", "Geum", "Gladiolus", "Gomphrena", "Gypsophila", "Hedera", "Helenium", "Helianthus", "Helichrysum", "Heliotropium",
            "Helleborus", "Hemerocallis", "Heuchera", "Hibiscus", "Hosta", "Hyacinthus", "Hydrangea", "Iberis", "Impatiens", "Ipomoea",
            "Iris", "Ixia", "Jasminum", "Kalanchoe", "Kniphofia", "Lamium", "Lantana", "Lathyrus", "Lavandula", "Lavatera",
            "Leucanthemum", "Liatris", "Lilium", "Limonium", "Linaria", "Linum", "Lobelia", "Lobularia", "Lunaria", "Lupinus",
            "Lychnis", "Lysimachia", "Malva", "Marigold", "Matthiola", "Meconopsis", "Mimulus", "Mirabilis", "Monarda", "Muscari",
            "Myosotis", "Narcissus", "Nasturtium", "Nemesia", "Nemophila", "Nepeta", "Nicotiana", "Nigella", "Oenothera", "Osteospermum",
            "Paeonia", "Pansy", "Papaver", "Pelargonium", "Penstemon", "Petunia", "Phlox", "Physalis", "Platycodon", "Plumbago"
        };
        return flowers;
    }

    private string[] ebru13()
    {
        string[] flowers = new string[]
        {
            "Polemonium", "Polyanthus", "Portulaca", "Primula", "Pulmonaria", "Ranunculus", "Reseda", "Rhodanthe", "Ricinus", "Rudbeckia",
            "Salvia", "Sanvitalia", "Scabiosa", "Schizanthus", "Sedum", "Senecio", "Silene", "Solidago", "Stachys", "Statice",
            "Stocks", "Sunflower", "Sweet Pea", "Tagetes", "Tanacetum", "Thalictrum", "Thunbergia", "Thymus", "Tithonia", "Torenia",
            "Tradescantia", "Trollius", "Tropaeolum", "Tulipa", "Verbascum", "Verbena", "Veronica", "Vinca", "Viola", "Wallflower",
            "Xeranthemum", "Zinnia", "Zonal Geranium", "Zygopetalum", "African Violet", "Alpine Pink", "Angel's Trumpet", "Baby's Breath", "Bachelor's Button", "Balloon Flower",
            "Bee Balm", "Bell Flower", "Bird of Paradise", "Black-eyed Susan", "Blanket Flower", "Bleeding Heart", "Blue Star", "Butterfly Bush", "Butterfly Weed", "Candytuft",
            "Cardinal Flower", "Carnation", "Cathedral Bells", "Chinese Lantern", "Christmas Rose", "Cockscomb", "Columbine", "Coral Bells", "Cornflower", "Crown Imperial",
            "Daffodil", "Dame's Rocket", "Dead Nettle", "Desert Rose", "Dusty Miller", "Dutch Iris", "Evening Primrose", "False Spirea", "Farewell to Spring", "Feverfew",
            "Fire Pink", "Flame Flower", "Floss Flower", "Forget-me-not", "Four O'Clock", "Foxglove", "French Marigold", "Garden Balsam", "Garden Phlox", "Gas Plant",
            "Globe Amaranth", "Globe Thistle", "Glory of the Snow", "Godetia", "Golden Marguerite", "Grape Hyacinth", "Hardy Geranium", "Hens and Chicks", "Hollyhock", "Honesty"
        };
        return flowers;
    }

    private string[] ebru14()
    {
        string[] flowers = new string[]
        {
            "Hosta", "Ice Plant", "Indian Blanket", "Japanese Anemone", "Japanese Painted Fern", "Johnny Jump Up", "Joseph's Coat", "King's Spear", "Lady's Mantle", "Lamb's Ear",
            "Larkspur", "Lemon Balm", "Lily of the Valley", "Love-in-a-Mist", "Love-lies-bleeding", "Madagascar Periwinkle", "Maltese Cross", "Meadow Rue", "Mexican Sunflower", "Mignonette",
            "Million Bells", "Monkey Flower", "Moonflower", "Morning Glory", "Moss Rose", "Mountain Laurel", "Nasturtium", "New Guinea Impatiens", "Night-scented Stock", "Obedient Plant",
            "Old Man's Beard", "Ornamental Cabbage", "Painted Daisy", "Painted Tongue", "Pasque Flower", "Patience Plant", "Periwinkle", "Persian Shield", "Pincushion Flower", "Pink Evening Primrose",
            "Pot Marigold", "Prairie Gentian", "Prickly Poppy", "Prince's Feather", "Queen Anne's Lace", "Red Hot Poker", "Rock Cress", "Rose Campion", "Rose of Sharon", "Russian Sage",
            "Sea Holly", "Sea Lavender", "Shasta Daisy", "Shooting Star", "Siberian Iris", "Silver Dust", "Snapdragon", "Snow-in-Summer", "Snow-on-the-Mountain", "Spider Flower",
            "Spotted Dead Nettle", "Star of Bethlehem", "Statice", "Stock", "Stonecrop", "Summer Cypress", "Swan River Daisy", "Sweet Alyssum", "Sweet Pea", "Sweet William",
            "Sword Lily", "Tidy Tips", "Tiger Lily", "Toad Lily", "Torch Lily", "Tree Mallow", "Tuberose", "Turtlehead", "Umbrella Plant", "Valerian",
            "Velvet Flower", "Venus Looking Glass", "Viper's Bugloss", "Virginia Bluebells", "Wake Robin", "Wax Begonia", "Wild Bergamot", "Wild Ginger", "Windflower", "Winter Aconite",
            "Wishbone Flower", "Wood Hyacinth", "Woolly Thyme", "Yarrow", "Yellow Archangel", "Yellow Bells", "Yellow Cosmos", "Yellow Flag", "Yellow Loosestrife", "Youth and Beauty"
        };
        return flowers;
    }

    private string[] ebru15()
    {
        string[] flowers = new string[]
        {
            "Zebra Plant", "Zephyr Lily", "Zigzag Goldenrod", "Zinnia", "Aethionema", "Agrostemma", "Alonsoa", "Ammobium", "Anagallis", "Anchusa",
            "Anemone", "Angelica", "Anoda", "Antirrhinum", "Arctotis", "Argemone", "Asarina", "Asperula", "Atriplex", "Bartonia",
            "Bassia", "Bidens", "Brachycome", "Brassica", "Briza", "Bromus", "Calandrinia", "Calceolaria", "Callirhoe", "Callistephus",
            "Campanula", "Capsella", "Cardiospermum", "Carthamus", "Catananche", "Cerinthe", "Chaenostoma", "Chamaemelum", "Cheiranthus", "Chenopodium",
            "Chrysanthemum", "Cirsium", "Clarkia", "Cleome", "Cobaea", "Collinsia", "Consolida", "Convolvulus", "Coreopsis", "Corydalis",
            "Cosmos", "Craspedia", "Crepis", "Cuphea", "Cynoglossum", "Dahlia", "Datura", "Daucus", "Delphinium", "Dianthus",
            "Diascia", "Didiscus", "Digitalis", "Dimorphotheca", "Dipsacus", "Dorotheanthus", "Dracocephalum", "Dyssodia", "Eccremocarpus", "Echium",
            "Emilia", "Eragrostis", "Eryngium", "Erysimum", "Eschscholzia", "Eucalyptus", "Euphorbia", "Eutoca", "Exacum", "Felicia",
            "Foeniculum", "Gaillardia", "Gazania", "Gilia", "Glaucium", "Gomphrena", "Grasses", "Gypsophila", "Helianthus", "Helichrysum",
            "Heliophila", "Heliotropium", "Hesperis", "Hibiscus", "Hordeum", "Hunnemannia", "Iberis", "Impatiens", "Ipomoea", "Isatis"
        };
        return flowers;
    }

    private string[] ebru16()
    {
        string[] flowers = new string[]
        {
            "Kochia", "Lagurus", "Lathyrus", "Lavatera", "Layia", "Lepidium", "Leptosiphon", "Limnanthes", "Limonium", "Linaria",
            "Linum", "Lobelia", "Lobularia", "Lonas", "Lunaria", "Lupinus", "Lychnis", "Malcolmia", "Malope", "Malva",
            "Matricaria", "Matthiola", "Meconopsis", "Mentzelia", "Mesembryanthemum", "Mimulus", "Mirabilis", "Moluccella", "Myosotis", "Nemesia",
            "Nemophila", "Nicandra", "Nicotiana", "Nierembergia", "Nigella", "Nolana", "Oenothera", "Omphalodes", "Onopordum", "Orlaya",
            "Osteospermum", "Oxypetalum", "Papaver", "Pelargonium", "Pennisetum", "Perilla", "Petunia", "Phacelia", "Phlox", "Physalis",
            "Platystemon", "Plectranthus", "Polemonium", "Polygonum", "Portulaca", "Proboscidea", "Psylliostachys", "Ptilotus", "Quamoclit", "Reseda",
            "Rhodanthe", "Ricinus", "Rudbeckia", "Salpiglossis", "Salvia", "Sanvitalia", "Saponaria", "Scabiosa", "Schizanthus", "Schizophragma",
            "Sedum", "Senecio", "Setaria", "Silene", "Silybum", "Solanum", "Sorghum", "Statice", "Sutera", "Tagetes",
            "Tanacetum", "Thunbergia", "Tithonia", "Torenia", "Trachelium", "Trachymene", "Trifolium", "Tropaeolum", "Tweedia", "Urtica",
            "Venidium", "Verbascum", "Verbena", "Veronica", "Vinca", "Viola", "Viscaria", "Wahlenbergia", "Xeranthemum", "Xerochrysum"
        };
        return flowers;
    }

    private string[] ebru17()
    {
        string[] flowers = new string[]
        {
            "Zea", "Zinnia", "Zizania", "Ageratum", "Alcea", "Amaranthus", "Antirrhinum", "Aquilegia", "Aster", "Begonia",
            "Calendula", "Callistephus", "Campanula", "Celosia", "Centaurea", "Chrysanthemum", "Clarkia", "Cleome", "Coleus", "Consolida",
            "Convolvulus", "Coreopsis", "Cosmos", "Dahlia", "Delphinium", "Dianthus", "Digitalis", "Eschscholzia", "Gaillardia", "Gazania",
            "Gomphrena", "Gypsophila", "Helianthus", "Helichrysum", "Iberis", "Impatiens", "Ipomoea", "Lathyrus", "Lavatera", "Limonium",
            "Lobelia", "Lupinus", "Matthiola", "Mirabilis", "Moluccella", "Myosotis", "Nasturtium", "Nemesia", "Nicotiana", "Nigella",
            "Papaver", "Petunia", "Phlox", "Portulaca", "Rudbeckia", "Salvia", "Scabiosa", "Tagetes", "Tropaeolum", "Verbena",
            "Viola", "Zinnia", "Abronia", "Acanthus", "Achillea", "Aconitum", "Actaea", "Adenophora", "Adonis", "Aegopodium",
            "Aethionema", "Agastache", "Agave", "Ageratum", "Agrostemma", "Ajuga", "Alcea", "Alchemilla", "Allium", "Alstroemeria",
            "Alyssum", "Amaranthus", "Amaryllis", "Ammi", "Ammobium", "Anemone", "Angelica", "Angelonia", "Anigozanthos", "Antennaria",
            "Anthemis", "Anthurium", "Antirrhinum", "Aquilegia", "Arabis", "Arctotis", "Arenaria", "Armeria", "Arnica", "Artemisia"
        };
        return flowers;
    }

    private string[] ebru18()
    {
        string[] flowers = new string[]
        {
            "Arum", "Aruncus", "Asarum", "Asclepias", "Asparagus", "Asperula", "Aster", "Astilbe", "Astrantia", "Aubrieta",
            "Aurinia", "Azalea", "Bacopa", "Baptisia", "Begonia", "Bellis", "Bergenia", "Betonica", "Bidens", "Boltonia",
            "Bougainvillea", "Brachyscome", "Briza", "Browallia", "Brunnera", "Buddleja", "Buphtalmum", "Bupleurum", "Caladium", "Calamagrostis",
            "Calandrinia", "Calceolaria", "Calendula", "Calibrachoa", "Calla", "Callirhoe", "Callistemon", "Callistephus", "Calluna", "Caltha",
            "Camassia", "Camellia", "Campanula", "Canna", "Capsicum", "Carex", "Catananche", "Catharanthus", "Catmint", "Celosia",
            "Centaurea", "Centranthus", "Cerastium", "Ceratostigma", "Chaenostoma", "Chamaecyparis", "Chelone", "Chionodoxa", "Chrysanthemum", "Cimicifuga",
            "Cineraria", "Clarkia", "Clematis", "Cleome", "Clivia", "Cobaea", "Colchicum", "Coleus", "Collinsia", "Colocasia",
            "Commelina", "Consolida", "Convallaria", "Convolvulus", "Coreopsis", "Cornus", "Cortaderia", "Corydalis", "Cosmos", "Cotinus",
            "Crambe", "Craspedia", "Crinum", "Crocosmia", "Crocus", "Cuphea", "Cyclamen", "Cynara", "Cynoglossum", "Dahlia",
            "Darmera", "Datura", "Daucus", "Delphinium", "Dendrobium", "Deschampsia", "Dianthus", "Diascia", "Dicentra", "Dictamnus"
        };
        return flowers;
    }

    private string[] ebru19()
    {
        string[] flowers = new string[]
        {
            "Didiscus", "Digitalis", "Dimorphotheca", "Dipsacus", "Dodecatheon", "Doronicum", "Dracaena", "Dracocephalum", "Dryopteris", "Echinacea",
            "Echinops", "Echium", "Elymus", "Emilia", "Epilobium", "Epimedium", "Eragrostis", "Eremurus", "Erica", "Erigeron",
            "Eryngium", "Erysimum", "Eschscholzia", "Eucalyptus", "Eucomis", "Eupatorium", "Euphorbia", "Eustoma", "Exacum", "Fallopia",
            "Felicia", "Festuca", "Filipendula", "Foeniculum", "Fragaria", "Fritillaria", "Fuchsia", "Gaillardia", "Galanthus", "Galega",
            "Gaura", "Gazania", "Gentiana", "Geranium", "Gerbera", "Geum", "Gilia", "Gladiolus", "Glaucium", "Globularia",
            "Gomphrena", "Grasses", "Gunnera", "Gypsophila", "Hakonechloa", "Hamamelis", "Hedera", "Hedychium", "Helenium", "Helianthemum",
            "Helianthus", "Helichrysum", "Heliophila", "Heliopsis", "Heliotropium", "Helleborus", "Hemerocallis", "Hepatica", "Hesperis", "Heuchera",
            "Hibiscus", "Hordeum", "Hosta", "Hunnemannia", "Hyacinthoides", "Hyacinthus", "Hydrangea", "Hyssopus", "Iberis", "Ilex",
            "Impatiens", "Imperata", "Incarvillea", "Inula", "Ipomoea", "Iris", "Isatis", "Ixia", "Jasione", "Jasminum",
            "Juncus", "Kalanchoe", "Kirengeshoma", "Knautia", "Kniphofia", "Kochia", "Lagurus", "Lamium", "Lantana", "Lathyrus"
        };
        return flowers;
    }

    private string[] ebru20()
    {
        string[] flowers = new string[]
        {
            "Lavandula", "Lavatera", "Layia", "Leonotis", "Lepidium", "Leptosiphon", "Leucanthemum", "Leucojum", "Liatris", "Libertia",
            "Ligularia", "Lilium", "Limnanthes", "Limonium", "Linaria", "Linum", "Liriope", "Lobelia", "Lobularia", "Lonas",
            "Lunaria", "Lupinus", "Lychnis", "Lycoris", "Lysimachia", "Lythrum", "Macleaya", "Malcolmia", "Malope", "Malva",
            "Marrubium", "Matricaria", "Matthiola", "Meconopsis", "Melica", "Mentha", "Mentzelia", "Mertensia", "Mesembryanthemum", "Mimulus",
            "Mirabilis", "Miscanthus", "Molinia", "Moluccella", "Monarda", "Muscari", "Myosotis", "Narcissus", "Nasturtium", "Nemesia",
            "Nemophila", "Nepeta", "Nerine", "Nicandra", "Nicotiana", "Nierembergia", "Nigella", "Nolana", "Oenothera", "Omphalodes",
            "Onopordum", "Ophiopogon", "Origanum", "Orlaya", "Ornithogalum", "Osteospermum", "Oxalis", "Oxypetalum", "Paeonia", "Panicum",
            "Pansy", "Papaver", "Passiflora", "Pelargonium", "Pennisetum", "Penstemon", "Perilla", "Persicaria", "Petunia", "Phacelia",
            "Phalaris", "Phleum", "Phlox", "Phormium", "Physalis", "Physostegia", "Phyteuma", "Platycodon", "Plectranthus", "Plumbago",
            "Polemonium", "Polygonatum", "Polygonum", "Polystichum", "Portulaca", "Potentilla", "Primula", "Proboscidea", "Prunella", "Psylliostachys"
        };
        return flowers;
    }

    private string[] ebru21()
    {
        string[] flowers = new string[]
        {
            "Ptilotus", "Pulmonaria", "Pulsatilla", "Quamoclit", "Ranunculus", "Reseda", "Rhodanthe", "Rhodiola", "Ricinus", "Rodgersia",
            "Romneya", "Rudbeckia", "Ruta", "Sagina", "Salpiglossis", "Salvia", "Sanguinaria", "Sanguisorba", "Sanvitalia", "Saponaria",
            "Saxifraga", "Scabiosa", "Schizanthus", "Schizophragma", "Scilla", "Sedum", "Sempervivum", "Senecio", "Setaria", "Sidalcea",
            "Silene", "Silybum", "Sisyrinchium", "Smilacina", "Solanum", "Solidago", "Sorghum", "Stachys", "Statice", "Stipa",
            "Stokesia", "Sutera", "Symphytum", "Tagetes", "Tanacetum", "Tellima", "Thalictrum", "Thermopsis", "Thunbergia", "Thymus",
            "Tiarella", "Tigridia", "Tithonia", "Torenia", "Trachelium", "Trachymene", "Tradescantia", "Trifolium", "Trillium", "Tritoma",
            "Trollius", "Tropaeolum", "Tulipa", "Tweedia", "Typha", "Urtica", "Uvularia", "Valeriana", "Venidium", "Verbascum",
            "Verbena", "Vernonia", "Veronica", "Viburnum", "Vinca", "Viola", "Viscaria", "Wahlenbergia", "Waldsteinia", "Weigela",
            "Xeranthemum", "Xerochrysum", "Yucca", "Zantedeschia", "Zea", "Zinnia", "Zizania", "Zygopetalum", "Abutilon", "Acacia",
            "Achillea", "Aconitum", "Adenium", "Agapanthus", "Ageratum", "Ajuga", "Alcea", "Alchemilla", "Allium", "Alstroemeria"
        };
        return flowers;
    }

    private string[] ebru22()
    {
        string[] flowers = new string[]
        {
            "Alyssum", "Amaranthus", "Amaryllis", "Anemone", "Angelonia", "Anthurium", "Antirrhinum", "Aquilegia", "Arabis", "Arctotis",
            "Armeria", "Artemisia", "Asclepias", "Aster", "Astilbe", "Aubrieta", "Azalea", "Bacopa", "Baptisia", "Begonia",
            "Bellis", "Bergenia", "Bougainvillea", "Brachyscome", "Browallia", "Buddleja", "Caladium", "Calendula", "Calibrachoa", "Calla",
            "Callistephus", "Camellia", "Campanula", "Canna", "Capsicum", "Catharanthus", "Celosia", "Centaurea", "Centranthus", "Cerastium",
            "Cheiranthus", "Chrysanthemum", "Cineraria", "Clarkia", "Cleome", "Coleus", "Convolvulus", "Coreopsis", "Cosmos", "Crocosmia",
            "Crocus", "Cyclamen", "Dahlia", "Delphinium", "Dianthus", "Diascia", "Digitalis", "Dimorphotheca", "Doronicum", "Echinacea",
            "Echinops", "Echium", "Eryngium", "Erysimum", "Eschscholzia", "Euphorbia", "Eustoma", "Felicia", "Fuchsia", "Gaillardia",
            "Galanthus", "Gazania", "Gentiana", "Geranium", "Gerbera", "Geum", "Gladiolus", "Gomphrena", "Gypsophila", "Hedera",
            "Helenium", "Helianthus", "Helichrysum", "Heliotropium", "Helleborus", "Hemerocallis", "Heuchera", "Hibiscus", "Hosta", "Hyacinthus",
            "Hydrangea", "Iberis", "Impatiens", "Ipomoea", "Iris", "Ixia", "Jasminum", "Kalanchoe", "Kniphofia", "Lamium"
        };
        return flowers;
    }

    private string[] ebru23()
    {
        string[] flowers = new string[]
        {
            "Lantana", "Lathyrus", "Lavandula", "Lavatera", "Leucanthemum", "Liatris", "Lilium", "Limonium", "Linaria", "Linum",
            "Lobelia", "Lobularia", "Lunaria", "Lupinus", "Lychnis", "Lysimachia", "Malva", "Marigold", "Matthiola", "Meconopsis",
            "Mimulus", "Mirabilis", "Monarda", "Muscari", "Myosotis", "Narcissus", "Nasturtium", "Nemesia", "Nemophila", "Nepeta",
            "Nicotiana", "Nigella", "Oenothera", "Osteospermum", "Paeonia", "Pansy", "Papaver", "Pelargonium", "Penstemon", "Petunia",
            "Phlox", "Physalis", "Platycodon", "Plumbago", "Polemonium", "Polyanthus", "Portulaca", "Primula", "Pulmonaria", "Ranunculus",
            "Reseda", "Rhodanthe", "Ricinus", "Rudbeckia", "Salvia", "Sanvitalia", "Scabiosa", "Schizanthus", "Sedum", "Senecio",
            "Silene", "Solidago", "Stachys", "Statice", "Stocks", "Sunflower", "Sweet Pea", "Tagetes", "Tanacetum", "Thalictrum",
            "Thunbergia", "Thymus", "Tithonia", "Torenia", "Tradescantia", "Trollius", "Tropaeolum", "Tulipa", "Verbascum", "Verbena",
            "Veronica", "Vinca", "Viola", "Wallflower", "Xeranthemum", "Zinnia", "Zonal Geranium", "Zygopetalum", "African Violet", "Alpine Pink",
            "Angel's Trumpet", "Baby's Breath", "Bachelor's Button", "Balloon Flower", "Bee Balm", "Bell Flower", "Bird of Paradise", "Black-eyed Susan"
        };
        return flowers;
    }

    private string[] ebru24()
    {
        string[] flowers = new string[]
        {
            "Blanket Flower", "Bleeding Heart", "Blue Star", "Butterfly Bush", "Butterfly Weed", "Candytuft", "Cardinal Flower", "Carnation", "Cathedral Bells", "Chinese Lantern",
            "Christmas Rose", "Cockscomb", "Columbine", "Coral Bells", "Cornflower", "Crown Imperial", "Daffodil", "Dame's Rocket", "Dead Nettle", "Desert Rose",
            "Dusty Miller", "Dutch Iris", "Evening Primrose", "False Spirea", "Farewell to Spring", "Feverfew", "Fire Pink", "Flame Flower", "Floss Flower", "Forget-me-not",
            "Four O'Clock", "Foxglove", "French Marigold", "Garden Balsam", "Garden Phlox", "Gas Plant", "Globe Amaranth", "Globe Thistle", "Glory of the Snow", "Godetia",
            "Golden Marguerite", "Grape Hyacinth", "Hardy Geranium", "Hens and Chicks", "Hollyhock", "Honesty", "Hosta", "Ice Plant", "Indian Blanket", "Japanese Anemone",
            "Japanese Painted Fern", "Johnny Jump Up", "Joseph's Coat", "King's Spear", "Lady's Mantle", "Lamb's Ear", "Larkspur", "Lemon Balm", "Lily of the Valley", "Love-in-a-Mist",
            "Love-lies-bleeding", "Madagascar Periwinkle", "Maltese Cross", "Meadow Rue", "Mexican Sunflower", "Mignonette", "Million Bells", "Monkey Flower", "Moonflower", "Morning Glory",
            "Moss Rose", "Mountain Laurel", "Nasturtium", "New Guinea Impatiens", "Night-scented Stock", "Obedient Plant", "Old Man's Beard", "Ornamental Cabbage", "Painted Daisy", "Painted Tongue",
            "Pasque Flower", "Patience Plant", "Periwinkle", "Persian Shield", "Pincushion Flower", "Pink Evening Primrose", "Pot Marigold", "Prairie Gentian", "Prickly Poppy", "Prince's Feather",
            "Queen Anne's Lace", "Red Hot Poker", "Rock Cress", "Rose Campion", "Rose of Sharon", "Russian Sage", "Sea Holly", "Sea Lavender", "Shasta Daisy", "Shooting Star"
        };
        return flowers;
    }

    private string[] ebru25()
    {
        string[] flowers = new string[]
        {
            "Siberian Iris", "Silver Dust", "Snapdragon", "Snow-in-Summer", "Snow-on-the-Mountain", "Spider Flower", "Spotted Dead Nettle", "Star of Bethlehem", "Statice", "Stock",
            "Stonecrop", "Summer Cypress", "Swan River Daisy", "Sweet Alyssum", "Sweet Pea", "Sweet William", "Sword Lily", "Tidy Tips", "Tiger Lily", "Toad Lily",
            "Torch Lily", "Tree Mallow", "Tuberose", "Turtlehead", "Umbrella Plant", "Valerian", "Velvet Flower", "Venus Looking Glass", "Viper's Bugloss", "Virginia Bluebells",
            "Wake Robin", "Wax Begonia", "Wild Bergamot", "Wild Ginger", "Windflower", "Winter Aconite", "Wishbone Flower", "Wood Hyacinth", "Woolly Thyme", "Yarrow",
            "Yellow Archangel", "Yellow Bells", "Yellow Cosmos", "Yellow Flag", "Yellow Loosestrife", "Youth and Beauty", "Zebra Plant", "Zephyr Lily", "Zigzag Goldenrod", "Zinnia",
            "Aethionema", "Agrostemma", "Alonsoa", "Ammobium", "Anagallis", "Anchusa", "Anemone", "Angelica", "Anoda", "Antirrhinum",
            "Arctotis", "Argemone", "Asarina", "Asperula", "Atriplex", "Bartonia", "Bassia", "Bidens", "Brachycome", "Brassica",
            "Briza", "Bromus", "Calandrinia", "Calceolaria", "Callirhoe", "Callistephus", "Campanula", "Capsella", "Cardiospermum", "Carthamus",
            "Catananche", "Cerinthe", "Chaenostoma", "Chamaemelum", "Cheiranthus", "Chenopodium", "Chrysanthemum", "Cirsium", "Clarkia", "Cleome",
            "Cobaea", "Collinsia", "Consolida", "Convolvulus", "Coreopsis", "Corydalis", "Cosmos", "Craspedia", "Crepis", "Cuphea"
        };
        return flowers;
    }

    private string[] ebru26()
    {
        string[] flowers = new string[]
        {
            "Cynoglossum", "Dahlia", "Datura", "Daucus", "Delphinium", "Dianthus", "Diascia", "Didiscus", "Digitalis", "Dimorphotheca",
            "Dipsacus", "Dorotheanthus", "Dracocephalum", "Dyssodia", "Eccremocarpus", "Echium", "Emilia", "Eragrostis", "Eryngium", "Erysimum",
            "Eschscholzia", "Eucalyptus", "Euphorbia", "Eutoca", "Exacum", "Felicia", "Foeniculum", "Gaillardia", "Gazania", "Gilia",
            "Glaucium", "Gomphrena", "Grasses", "Gypsophila", "Helianthus", "Helichrysum", "Heliophila", "Heliotropium", "Hesperis", "Hibiscus",
            "Hordeum", "Hunnemannia", "Iberis", "Impatiens", "Ipomoea", "Isatis", "Kochia", "Lagurus", "Lathyrus", "Lavatera",
            "Layia", "Lepidium", "Leptosiphon", "Limnanthes", "Limonium", "Linaria", "Linum", "Lobelia", "Lobularia", "Lonas",
            "Lunaria", "Lupinus", "Lychnis", "Malcolmia", "Malope", "Malva", "Matricaria", "Matthiola", "Meconopsis", "Mentzelia",
            "Mesembryanthemum", "Mimulus", "Mirabilis", "Moluccella", "Myosotis", "Nemesia", "Nemophila", "Nicandra", "Nicotiana", "Nierembergia",
            "Nigella", "Nolana", "Oenothera", "Omphalodes", "Onopordum", "Orlaya", "Osteospermum", "Oxypetalum", "Papaver", "Pelargonium",
            "Pennisetum", "Perilla", "Petunia", "Phacelia", "Phlox", "Physalis", "Platystemon", "Plectranthus", "Polemonium", "Polygonum"
        };
        return flowers;
    }

    private string[] ebru27()
    {
        string[] flowers = new string[]
        {
            "Portulaca", "Proboscidea", "Psylliostachys", "Ptilotus", "Quamoclit", "Reseda", "Rhodanthe", "Ricinus", "Rudbeckia", "Salpiglossis",
            "Salvia", "Sanvitalia", "Saponaria", "Scabiosa", "Schizanthus", "Schizophragma", "Sedum", "Senecio", "Setaria", "Silene",
            "Silybum", "Solanum", "Sorghum", "Statice", "Sutera", "Tagetes", "Tanacetum", "Thunbergia", "Tithonia", "Torenia",
            "Trachelium", "Trachymene", "Trifolium", "Tropaeolum", "Tweedia", "Urtica", "Venidium", "Verbascum", "Verbena", "Veronica",
            "Vinca", "Viola", "Viscaria", "Wahlenbergia", "Xeranthemum", "Xerochrysum", "Zea", "Zinnia", "Zizania", "Ageratum",
            "Alcea", "Amaranthus", "Antirrhinum", "Aquilegia", "Aster", "Begonia", "Calendula", "Callistephus", "Campanula", "Celosia",
            "Centaurea", "Chrysanthemum", "Clarkia", "Cleome", "Coleus", "Consolida", "Convolvulus", "Coreopsis", "Cosmos", "Dahlia",
            "Delphinium", "Dianthus", "Digitalis", "Eschscholzia", "Gaillardia", "Gazania", "Gomphrena", "Gypsophila", "Helianthus", "Helichrysum",
            "Iberis", "Impatiens", "Ipomoea", "Lathyrus", "Lavatera", "Limonium", "Lobelia", "Lupinus", "Matthiola", "Mirabilis",
            "Moluccella", "Myosotis", "Nasturtium", "Nemesia", "Nicotiana", "Nigella", "Papaver", "Petunia", "Phlox", "Portulaca"
        };
        return flowers;
    }

    private string[] ebru28()
    {
        string[] flowers = new string[]
        {
            "Rudbeckia", "Salvia", "Scabiosa", "Tagetes", "Tropaeolum", "Verbena", "Viola", "Zinnia", "Abronia", "Acanthus",
            "Achillea", "Aconitum", "Actaea", "Adenophora", "Adonis", "Aegopodium", "Aethionema", "Agastache", "Agave", "Ageratum",
            "Agrostemma", "Ajuga", "Alcea", "Alchemilla", "Allium", "Alstroemeria", "Alyssum", "Amaranthus", "Amaryllis", "Ammi",
            "Ammobium", "Anemone", "Angelica", "Angelonia", "Anigozanthos", "Antennaria", "Anthemis", "Anthurium", "Antirrhinum", "Aquilegia",
            "Arabis", "Arctotis", "Arenaria", "Armeria", "Arnica", "Artemisia", "Arum", "Aruncus", "Asarum", "Asclepias",
            "Asparagus", "Asperula", "Aster", "Astilbe", "Astrantia", "Aubrieta", "Aurinia", "Azalea", "Bacopa", "Baptisia",
            "Begonia", "Bellis", "Bergenia", "Betonica", "Bidens", "Boltonia", "Bougainvillea", "Brachyscome", "Briza", "Browallia",
            "Brunnera", "Buddleja", "Buphtalmum", "Bupleurum", "Caladium", "Calamagrostis", "Calandrinia", "Calceolaria", "Calendula", "Calibrachoa",
            "Calla", "Callirhoe", "Callistemon", "Callistephus", "Calluna", "Caltha", "Camassia", "Camellia", "Campanula", "Canna",
            "Capsicum", "Carex", "Catananche", "Catharanthus", "Catmint", "Celosia", "Centaurea", "Centranthus", "Cerastium", "Ceratostigma"
        };
        return flowers;
    }

    private string[] ebru29()
    {
        string[] flowers = new string[]
        {
            "Chaenostoma", "Chamaecyparis", "Chelone", "Chionodoxa", "Chrysanthemum", "Cimicifuga", "Cineraria", "Clarkia", "Clematis", "Cleome",
            "Clivia", "Cobaea", "Colchicum", "Coleus", "Collinsia", "Colocasia", "Commelina", "Consolida", "Convallaria", "Convolvulus",
            "Coreopsis", "Cornus", "Cortaderia", "Corydalis", "Cosmos", "Cotinus", "Crambe", "Craspedia", "Crinum", "Crocosmia",
            "Crocus", "Cuphea", "Cyclamen", "Cynara", "Cynoglossum", "Dahlia", "Darmera", "Datura", "Daucus", "Delphinium",
            "Dendrobium", "Deschampsia", "Dianthus", "Diascia", "Dicentra", "Dictamnus", "Didiscus", "Digitalis", "Dimorphotheca", "Dipsacus",
            "Dodecatheon", "Doronicum", "Dracaena", "Dracocephalum", "Dryopteris", "Echinacea", "Echinops", "Echium", "Elymus", "Emilia",
            "Epilobium", "Epimedium", "Eragrostis", "Eremurus", "Erica", "Erigeron", "Eryngium", "Erysimum", "Eschscholzia", "Eucalyptus",
            "Eucomis", "Eupatorium", "Euphorbia", "Eustoma", "Exacum", "Fallopia", "Felicia", "Festuca", "Filipendula", "Foeniculum",
            "Fragaria", "Fritillaria", "Fuchsia", "Gaillardia", "Galanthus", "Galega", "Gaura", "Gazania", "Gentiana", "Geranium",
            "Gerbera", "Geum", "Gilia", "Gladiolus", "Glaucium", "Globularia", "Gomphrena", "Grasses", "Gunnera", "Gypsophila"
        };
        return flowers;
    }

    private string[] ebru30()
    {
        string[] flowers = new string[]
        {
            "Hakonechloa", "Hamamelis", "Hedera", "Hedychium", "Helenium", "Helianthemum", "Helianthus", "Helichrysum", "Heliophila", "Heliopsis",
            "Heliotropium", "Helleborus", "Hemerocallis", "Hepatica", "Hesperis", "Heuchera", "Hibiscus", "Hordeum", "Hosta", "Hunnemannia",
            "Hyacinthoides", "Hyacinthus", "Hydrangea", "Hyssopus", "Iberis", "Ilex", "Impatiens", "Imperata", "Incarvillea", "Inula",
            "Ipomoea", "Iris", "Isatis", "Ixia", "Jasione", "Jasminum", "Juncus", "Kalanchoe", "Kirengeshoma", "Knautia",
            "Kniphofia", "Kochia", "Lagurus", "Lamium", "Lantana", "Lathyrus", "Lavandula", "Lavatera", "Layia", "Leonotis",
            "Lepidium", "Leptosiphon", "Leucanthemum", "Leucojum", "Liatris", "Libertia", "Ligularia", "Lilium", "Limnanthes", "Limonium",
            "Linaria", "Linum", "Liriope", "Lobelia", "Lobularia", "Lonas", "Lunaria", "Lupinus", "Lychnis", "Lycoris",
            "Lysimachia", "Lythrum", "Macleaya", "Malcolmia", "Malope", "Malva", "Marrubium", "Matricaria", "Matthiola", "Meconopsis",
            "Melica", "Mentha", "Mentzelia", "Mertensia", "Mesembryanthemum", "Mimulus", "Mirabilis", "Miscanthus", "Molinia", "Moluccella",
            "Monarda", "Muscari", "Myosotis", "Narcissus", "Nasturtium", "Nemesia", "Nemophila", "Nepeta", "Nerine", "Nicandra"
        };
        return flowers;
    }

    private void btnEbrushow_Click(object sender, EventArgs e)
    {
        // Tüm ebru metodlarını çağır ve sonuçları birleştir
        StringBuilder allFlowers = new StringBuilder();
        int totalWords = 0;

        // ebru1'den ebru30'a kadar tüm metodları çağır
        for (int i = 1; i <= 30; i++)
        {
            string[] flowers = null;

            switch (i)
            {
                case 1: flowers = ebru1(); break;
                case 2: flowers = ebru2(); break;
                case 3: flowers = ebru3(); break;
                case 4: flowers = ebru4(); break;
                case 5: flowers = ebru5(); break;
                case 6: flowers = ebru6(); break;
                case 7: flowers = ebru7(); break;
                case 8: flowers = ebru8(); break;
                case 9: flowers = ebru9(); break;
                case 10: flowers = ebru10(); break;
                case 11: flowers = ebru11(); break;
                case 12: flowers = ebru12(); break;
                case 13: flowers = ebru13(); break;
                case 14: flowers = ebru14(); break;
                case 15: flowers = ebru15(); break;
                case 16: flowers = ebru16(); break;
                case 17: flowers = ebru17(); break;
                case 18: flowers = ebru18(); break;
                case 19: flowers = ebru19(); break;
                case 20: flowers = ebru20(); break;
                case 21: flowers = ebru21(); break;
                case 22: flowers = ebru22(); break;
                case 23: flowers = ebru23(); break;
                case 24: flowers = ebru24(); break;
                case 25: flowers = ebru25(); break;
                case 26: flowers = ebru26(); break;
                case 27: flowers = ebru27(); break;
                case 28: flowers = ebru28(); break;
                case 29: flowers = ebru29(); break;
                case 30: flowers = ebru30(); break;
            }

            if (flowers != null)
            {
                allFlowers.AppendLine($"=== EBRU{i} ({flowers.Length} çiçek) ===");
                for (int j = 0; j < flowers.Length; j++)
                {
                    allFlowers.Append(flowers[j]);
                    if (j < flowers.Length - 1)
                        allFlowers.Append(", ");
                }
                allFlowers.AppendLine();
                allFlowers.AppendLine();
                totalWords += flowers.Length;
            }
        }

        string message = allFlowers.ToString();
        int tokenCount = message.Length / 4;
        int charCount = message.Length;

        // İstatistikleri göster
        string stats = $"=== EBRU METODLARI İSTATİSTİKLERİ ===\n" +
                      $"Toplam Metod Sayısı: 30\n" +
                      $"Toplam Kelime Sayısı: {totalWords}\n" +
                      $"Toplam Token Sayısı: {tokenCount}\n" +
                      $"Toplam Karakter Sayısı: {charCount}\n" +
                      $"Ortalama Kelime/Metod: {totalWords / 30}\n\n" +
                      $"{message}";

        // Sonucu txtOutput'a yaz
        if (txtOutput != null)
        {
            txtOutput.Text = stats;
        }
        else
        {
            MessageBox.Show(stats, "Ebru Metodları İçeriği", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
